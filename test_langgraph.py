#!/usr/bin/env python3
"""
Test script to verify LangGraph setup and basic functionality.
This script demonstrates the core concepts explained in the tutorial.
"""

print("🚀 Testing LangGraph Setup...")
print("=" * 50)

# Test 1: Import LangGraph
try:
    from langgraph.graph import Graph
    print("✅ LangGraph imported successfully!")
except ImportError as e:
    print(f"❌ Failed to import LangGraph: {e}")
    exit(1)

# Test 2: Create simple functions
def function1(input_text):
    """First processing function."""
    result = input_text + " from first function"
    print(f"🔄 Function 1: '{input_text}' → '{result}'")
    return result

def function2(input_text):
    """Second processing function."""
    result = input_text + " savita from second function"
    print(f"🔄 Function 2: '{input_text}' → '{result}'")
    return result

print("\n✅ Functions defined successfully!")

# Test 3: Create and test workflow
print("\n🏭 Building LangGraph Workflow...")
print("-" * 30)

# Create workflow
workflow = Graph()
print("✅ Created empty workflow")

# Add nodes
workflow.add_node("fun1", function1)
workflow.add_node("fun2", function2)
print("✅ Added nodes to workflow")

# Add edges
workflow.add_edge("fun1", "fun2")
print("✅ Connected nodes with edges")

# Set entry and exit points
workflow.set_entry_point("fun1")
workflow.set_finish_point("fun2")
print("✅ Set entry and exit points")

# Compile workflow
app = workflow.compile()
print("✅ Workflow compiled successfully!")

# Test 4: Run the workflow
print("\n🚀 Testing Workflow Execution...")
print("-" * 30)

test_input = "Hello LangGraph"
print(f"📥 Input: '{test_input}'")

result = app.invoke(test_input)
print(f"📤 Final Output: '{result}'")

# Test 5: Stream workflow execution
print("\n🔄 Testing Streaming Execution...")
print("-" * 30)

stream_input = "Learning AI Workflows"
print(f"📥 Streaming Input: '{stream_input}'")

for output in app.stream(stream_input):
    for node_name, node_result in output.items():
        print(f"📍 {node_name}: {node_result}")

print("\n🎉 All tests passed! LangGraph is working correctly!")
print("✅ You're ready to run the tutorial notebooks!")

# Test 6: Try AI integration (optional)
print("\n🤖 Testing AI Integration (Optional)...")
print("-" * 30)

try:
    from langchain_google_genai import ChatGoogleGenerativeAI
    print("✅ AI packages available!")
    print("💡 You can run the AI workflow tutorial!")
except ImportError:
    print("⚠️  AI packages not available - basic tutorial will work fine")

print("\n📚 Next Steps:")
print("1. Open Jupyter Notebook: jupyter notebook")
print("2. Start with: complete_tutorial.ipynb")
print("3. Then try: ai_workflow_tutorial.ipynb")
print("4. Read the README.md for detailed explanations")
