{"cells": [{"cell_type": "markdown", "id": "ai-tutorial-header", "metadata": {}, "source": ["# **LangGraph AI Integration Tutorial**\n", "\n", "## **Example 2: Real AI Workflow with Google Gemini**\n", "\n", "Now let's build a more practical example using real AI! We'll create a workflow that:\n", "1. **Takes a question** from the user\n", "2. **Processes it with Google Gemini AI** to get an answer\n", "3. **Counts the tokens** in the AI response\n", "4. **Returns both the answer and token count**\n", "\n", "### **🎯 Real-World Applications:**\n", "- **Customer Support**: Question → AI Answer → Quality Check\n", "- **Content Creation**: Prompt → AI Content → Word Count\n", "- **Data Analysis**: Query → AI Analysis → Result Validation\n", "\n", "---"]}, {"cell_type": "markdown", "id": "setup-ai", "metadata": {}, "source": ["## **Step 1: Setting Up AI Integration**\n", "\n", "**Important Note**: To use Google Gemini, you need an API key. For this tutorial, we'll show the structure without requiring the actual API key."]}, {"cell_type": "code", "execution_count": null, "id": "import-ai", "metadata": {}, "outputs": [], "source": ["# 📦 Import required packages\n", "from langgraph.graph import Graph\n", "from langchain_google_genai import ChatGoogleGenerativeAI\n", "from IPython.display import Image, display\n", "import os\n", "\n", "print(\"✅ AI packages imported successfully!\")"]}, {"cell_type": "markdown", "id": "ai-functions", "metadata": {}, "source": ["## **Step 2: Creating AI-Powered Functions**\n", "\n", "Let's create functions that work with real AI:"]}, {"cell_type": "code", "execution_count": null, "id": "define-ai-functions", "metadata": {}, "outputs": [], "source": ["# 🤖 FUNCTION 1: AI Response Generator\n", "def llm_function(user_input):\n", "    \"\"\"\n", "    Processes user input through Google Gemini AI.\n", "    \n", "    Args:\n", "        user_input (str): The user's question or prompt\n", "    \n", "    Returns:\n", "        str: AI-generated response\n", "    \"\"\"\n", "    try:\n", "        # Initialize the AI model\n", "        model = ChatGoogleGenerativeAI(model='gemini-1.5-flash')\n", "        \n", "        # Get AI response\n", "        response = model.invoke(user_input)\n", "        ai_answer = response.content\n", "        \n", "        print(f\"🤖 AI processed: '{user_input[:50]}...'\")\n", "        print(f\"📝 Generated {len(ai_answer)} characters\")\n", "        \n", "        return ai_answer\n", "        \n", "    except Exception as e:\n", "        # Fallback for demo purposes\n", "        fallback_response = f\"Demo response for: {user_input}. This is a simulated AI response that would normally come from Google Gemini. The response includes detailed information about the topic, explanations, and helpful insights that would be generated by the AI model.\"\n", "        print(f\"🤖 Demo mode: Using fallback response\")\n", "        return fallback_response\n", "\n", "# 📊 FUNCTION 2: Token Counter\n", "def token_counter(ai_response):\n", "    \"\"\"\n", "    Counts tokens (words) in the AI response.\n", "    \n", "    Args:\n", "        ai_response (str): The AI-generated text\n", "    \n", "    Returns:\n", "        str: Token count information\n", "    \"\"\"\n", "    # Simple word-based token counting\n", "    tokens = ai_response.split()\n", "    token_count = len(tokens)\n", "    \n", "    result = f\"📊 Analysis Complete: {token_count} tokens in the AI response\"\n", "    print(f\"📊 Counted {token_count} tokens\")\n", "    \n", "    return result\n", "\n", "print(\"✅ AI functions defined successfully!\")"]}, {"cell_type": "markdown", "id": "test-ai-functions", "metadata": {}, "source": ["## **Step 3: Testing AI Functions Individually**\n", "\n", "Let's test our AI functions before building the workflow:"]}, {"cell_type": "code", "execution_count": null, "id": "test-ai", "metadata": {}, "outputs": [], "source": ["# 🧪 Test the AI function\n", "print(\"Testing AI Function:\")\n", "print(\"=\" * 40)\n", "test_question = \"What is the capital of India?\"\n", "ai_response = llm_function(test_question)\n", "print(f\"\\n📤 AI Response: {ai_response[:100]}...\\n\")\n", "\n", "# 🧪 Test the token counter\n", "print(\"Testing Token Counter:\")\n", "print(\"=\" * 40)\n", "token_result = token_counter(ai_response)\n", "print(f\"📤 Token Count Result: {token_result}\")"]}, {"cell_type": "markdown", "id": "build-ai-workflow", "metadata": {}, "source": ["## **Step 4: Building the AI Workflow**\n", "\n", "Now let's create a workflow that combines AI processing with analysis:"]}, {"cell_type": "code", "execution_count": null, "id": "create-ai-workflow", "metadata": {}, "outputs": [], "source": ["# 🏭 Create AI Workflow\n", "print(\"🏭 Building AI Workflow...\")\n", "print(\"=\" * 40)\n", "\n", "# Step 1: Create the workflow container\n", "ai_workflow = Graph()\n", "print(\"✅ Created workflow container\")\n", "\n", "# Step 2: Add AI processing nodes\n", "ai_workflow.add_node(\"AI_Generator\", llm_function)\n", "ai_workflow.add_node(\"Token_Analyzer\", token_counter)\n", "print(\"✅ Added AI processing nodes\")\n", "\n", "# Step 3: Connect the nodes\n", "ai_workflow.add_edge(\"AI_Generator\", \"Token_Analyzer\")\n", "print(\"✅ Connected AI Generator → Token Analyzer\")\n", "\n", "# Step 4: Set workflow entry and exit points\n", "ai_workflow.set_entry_point(\"AI_Generator\")\n", "ai_workflow.set_finish_point(\"Token_Analyzer\")\n", "print(\"✅ Set entry point: AI_Generator\")\n", "print(\"✅ Set exit point: Token_Analyzer\")\n", "\n", "# Step 5: Compile the AI workflow\n", "ai_app = ai_workflow.compile()\n", "print(\"✅ AI Workflow compiled and ready!\")\n", "\n", "print(\"\\n🎯 Workflow Flow:\")\n", "print(\"   User Question → AI Generator → Token Analyzer → Final Result\")"]}, {"cell_type": "markdown", "id": "visualize-ai-workflow", "metadata": {}, "source": ["## **Step 5: Visualizing the AI Workflow**"]}, {"cell_type": "code", "execution_count": null, "id": "show-ai-diagram", "metadata": {}, "outputs": [], "source": ["# 📊 Display the AI workflow diagram\n", "try:\n", "    display(Image(ai_app.get_graph().draw_mermaid_png()))\n", "    print(\"📊 AI Workflow diagram displayed above!\")\n", "except Exception as e:\n", "    print(f\"Note: Diagram visualization requires additional setup\")\n", "    print(\"The workflow structure is: User Input → AI Generator → Token Analyzer → Result\")"]}, {"cell_type": "markdown", "id": "run-ai-workflow", "metadata": {}, "source": ["## **Step 6: Running the AI Workflow**\n", "\n", "Let's test our AI workflow with different questions:"]}, {"cell_type": "code", "execution_count": null, "id": "test-ai-workflow-simple", "metadata": {}, "outputs": [], "source": ["# 🚀 Test 1: Simple question\n", "print(\"🚀 Test 1: Simple Question\")\n", "print(\"=\" * 50)\n", "question1 = \"What is the capital of France?\"\n", "result1 = ai_app.invoke(question1)\n", "print(f\"\\n📤 Final Result: {result1}\")"]}, {"cell_type": "code", "execution_count": null, "id": "test-ai-workflow-complex", "metadata": {}, "outputs": [], "source": ["# 🚀 Test 2: Complex question (generates more tokens)\n", "print(\"🚀 Test 2: Complex Question\")\n", "print(\"=\" * 50)\n", "question2 = \"Explain artificial intelligence and its applications in detail\"\n", "result2 = ai_app.invoke(question2)\n", "print(f\"\\n📤 Final Result: {result2}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stream-ai-workflow", "metadata": {}, "outputs": [], "source": ["# 🔄 Test 3: Stream the workflow to see each step\n", "print(\"🔄 Test 3: Streaming AI Workflow\")\n", "print(\"=\" * 50)\n", "question3 = \"Tell me about machine learning in simple terms\"\n", "\n", "for output in ai_app.stream(question3):\n", "    for node_name, result in output.items():\n", "        print(f\"\\n📍 Output from {node_name}:\")\n", "        if node_name == \"AI_Generator\":\n", "            # Show first 200 characters of AI response\n", "            print(f\"   {result[:200]}...\")\n", "        else:\n", "            print(f\"   {result}\")\n", "        print(\"-\" * 40)"]}, {"cell_type": "markdown", "id": "workflow-benefits", "metadata": {}, "source": ["## **Step 7: Understanding the Benefits**\n", "\n", "### **🎯 What We've Accomplished:**\n", "\n", "1. **Modular Design**: Each function has a single responsibility\n", "2. **Easy Testing**: We can test each component individually\n", "3. **Reusable Workflow**: Can be used with any question\n", "4. **Scalable Architecture**: Easy to add more processing steps\n", "5. **Visual Understanding**: Can see the workflow structure\n", "\n", "### **🔧 Easy Extensions:**\n", "- Add sentiment analysis\n", "- Include language detection\n", "- Add response quality scoring\n", "- Include error handling nodes\n", "- Add logging and monitoring\n", "\n", "### **💡 Real-World Applications:**\n", "- **Customer Support Chatbots**\n", "- **Content Generation Pipelines**\n", "- **Document Analysis Systems**\n", "- **Educational AI Tutors**\n", "- **Research Assistant Tools**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}