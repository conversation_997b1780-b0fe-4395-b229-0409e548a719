# 📦 Import required packages
from langgraph.graph import Graph
from langchain_google_genai import ChatGoogleGenerativeAI
from IPython.display import Image, display
import os

print("✅ AI packages imported successfully!")

# 🤖 FUNCTION 1: AI Response Generator
def llm_function(user_input):
    """
    Processes user input through Google Gemini AI.
    
    Args:
        user_input (str): The user's question or prompt
    
    Returns:
        str: AI-generated response
    """
    try:
        # Initialize the AI model
        model = ChatGoogleGenerativeAI(model='gemini-1.5-flash')
        
        # Get AI response
        response = model.invoke(user_input)
        ai_answer = response.content
        
        print(f"🤖 AI processed: '{user_input[:50]}...'")
        print(f"📝 Generated {len(ai_answer)} characters")
        
        return ai_answer
        
    except Exception as e:
        # Fallback for demo purposes
        fallback_response = f"Demo response for: {user_input}. This is a simulated AI response that would normally come from Google Gemini. The response includes detailed information about the topic, explanations, and helpful insights that would be generated by the AI model."
        print(f"🤖 Demo mode: Using fallback response")
        return fallback_response

# 📊 FUNCTION 2: Token Counter
def token_counter(ai_response):
    """
    Counts tokens (words) in the AI response.
    
    Args:
        ai_response (str): The AI-generated text
    
    Returns:
        str: Token count information
    """
    # Simple word-based token counting
    tokens = ai_response.split()
    token_count = len(tokens)
    
    result = f"📊 Analysis Complete: {token_count} tokens in the AI response"
    print(f"📊 Counted {token_count} tokens")
    
    return result

print("✅ AI functions defined successfully!")

# 🧪 Test the AI function
print("Testing AI Function:")
print("=" * 40)
test_question = "What is the capital of India?"
ai_response = llm_function(test_question)
print(f"\n📤 AI Response: {ai_response[:100]}...\n")

# 🧪 Test the token counter
print("Testing Token Counter:")
print("=" * 40)
token_result = token_counter(ai_response)
print(f"📤 Token Count Result: {token_result}")

# 🏭 Create AI Workflow
print("🏭 Building AI Workflow...")
print("=" * 40)

# Step 1: Create the workflow container
ai_workflow = Graph()
print("✅ Created workflow container")

# Step 2: Add AI processing nodes
ai_workflow.add_node("AI_Generator", llm_function)
ai_workflow.add_node("Token_Analyzer", token_counter)
print("✅ Added AI processing nodes")

# Step 3: Connect the nodes
ai_workflow.add_edge("AI_Generator", "Token_Analyzer")
print("✅ Connected AI Generator → Token Analyzer")

# Step 4: Set workflow entry and exit points
ai_workflow.set_entry_point("AI_Generator")
ai_workflow.set_finish_point("Token_Analyzer")
print("✅ Set entry point: AI_Generator")
print("✅ Set exit point: Token_Analyzer")

# Step 5: Compile the AI workflow
ai_app = ai_workflow.compile()
print("✅ AI Workflow compiled and ready!")

print("\n🎯 Workflow Flow:")
print("   User Question → AI Generator → Token Analyzer → Final Result")

# 📊 Display the AI workflow diagram
try:
    display(Image(ai_app.get_graph().draw_mermaid_png()))
    print("📊 AI Workflow diagram displayed above!")
except Exception as e:
    print(f"Note: Diagram visualization requires additional setup")
    print("The workflow structure is: User Input → AI Generator → Token Analyzer → Result")

# 🚀 Test 1: Simple question
print("🚀 Test 1: Simple Question")
print("=" * 50)
question1 = "What is the capital of France?"
result1 = ai_app.invoke(question1)
print(f"\n📤 Final Result: {result1}")

# 🚀 Test 2: Complex question (generates more tokens)
print("🚀 Test 2: Complex Question")
print("=" * 50)
question2 = "Explain artificial intelligence and its applications in detail"
result2 = ai_app.invoke(question2)
print(f"\n📤 Final Result: {result2}")

# 🔄 Test 3: Stream the workflow to see each step
print("🔄 Test 3: Streaming AI Workflow")
print("=" * 50)
question3 = "Tell me about machine learning in simple terms"

for output in ai_app.stream(question3):
    for node_name, result in output.items():
        print(f"\n📍 Output from {node_name}:")
        if node_name == "AI_Generator":
            # Show first 200 characters of AI response
            print(f"   {result[:200]}...")
        else:
            print(f"   {result}")
        print("-" * 40)