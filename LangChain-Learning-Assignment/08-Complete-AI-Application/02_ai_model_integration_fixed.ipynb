{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🤖 Step 2: AI Model Integration Review (Fixed Version)\n", "\n", "## 🎯 What We're Building\n", "A professional AI model manager that can switch between different AI services and optimize performance.\n", "\n", "## 🧠 Why This Step Matters\n", "- **Flexibility**: Switch between models based on task requirements\n", "- **Cost Optimization**: Use cheaper models for simple tasks\n", "- **Performance**: Use faster models when speed matters\n", "- **Reliability**: Fallback options when one service is down\n", "\n", "## 🔄 Connection to Previous Learning\n", "This builds on **Assignment 02** but adds professional model management features."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.1: Complete AI Model Manager Class\n", "\n", "**What we're doing**: Creating a complete, professional AI model manager\n", "\n", "**Why we need this**: One class that handles everything - model switching, performance monitoring, error handling\n", "\n", "**Design Pattern**: This combines multiple patterns for a robust solution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Complete AI Model Manager - All features in one class\n", "import os\n", "import time\n", "import pandas as pd\n", "from typing import Optional, Dict, Any, List\n", "from dataclasses import dataclass\n", "from datetime import datetime\n", "from dotenv import load_dotenv\n", "from langchain_openai import ChatOpenAI\n", "from langchain_groq import ChatGroq\n", "\n", "@dataclass\n", "class ModelInfo:\n", "    \"\"\"Information about an AI model\"\"\"\n", "    name: str\n", "    provider: str\n", "    speed: str  # \"fast\", \"medium\", \"slow\"\n", "    cost: str   # \"low\", \"medium\", \"high\"\n", "    quality: str # \"good\", \"very good\", \"excellent\"\n", "    best_for: List[str]\n", "    context_length: int  # Maximum tokens\n", "\n", "class CompleteAIModelManager:\n", "    \"\"\"\n", "    Complete AI Model Manager with all features:\n", "    - Model management and switching\n", "    - Performance monitoring\n", "    - Intelligent model selection\n", "    - Error handling and fallbacks\n", "    \"\"\"\n", "    \n", "    def __init__(self):\n", "        \"\"\"Initialize the complete model manager\"\"\"\n", "        print(\"🚀 Initializing Complete AI Model Manager...\")\n", "        \n", "        # Load environment variables\n", "        load_dotenv()\n", "        self._setup_environment()\n", "        \n", "        # Initialize storage\n", "        self.models: Dict[str, Any] = {}\n", "        self.current_model: Optional[str] = None\n", "        self.performance_stats: Dict[str, Dict] = {}\n", "        self.model_info = self._get_model_info()\n", "        \n", "        # Initialize models\n", "        self._initialize_models()\n", "        \n", "        print(f\"✅ Manager initialized with {len(self.models)} models\")\n", "        print(f\"📊 Available models: {list(self.models.keys())}\")\n", "    \n", "    def _setup_environment(self):\n", "        \"\"\"Set up environment variables\"\"\"\n", "        os.environ[\"OPENAI_API_KEY\"] = os.getenv(\"OPENAI_API_KEY\", \"\")\n", "        os.environ[\"GROQ_API_KEY\"] = os.getenv(\"GROQ_API_KEY\", \"\")\n", "        os.environ[\"LANGCHAIN_API_KEY\"] = os.getenv(\"LANGCHAIN_API_KEY\", \"\")\n", "        os.environ[\"LANGCHAIN_PROJECT\"] = os.getenv(\"LANGCHAIN_PROJECT\", \"AI-Application\")\n", "        os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "    \n", "    def _get_model_info(self) -> Dict[str, ModelInfo]:\n", "        \"\"\"Get information about available models\"\"\"\n", "        return {\n", "            \"gpt-3.5-turbo\": ModelInfo(\n", "                name=\"gpt-3.5-turbo\",\n", "                provider=\"OpenAI\",\n", "                speed=\"medium\",\n", "                cost=\"medium\",\n", "                quality=\"very good\",\n", "                best_for=[\"general chat\", \"simple analysis\", \"quick responses\"],\n", "                context_length=4096\n", "            ),\n", "            \"gpt-4\": ModelInfo(\n", "                name=\"gpt-4\",\n", "                provider=\"OpenAI\",\n", "                speed=\"slow\",\n", "                cost=\"high\",\n", "                quality=\"excellent\",\n", "                best_for=[\"complex reasoning\", \"creative writing\", \"detailed analysis\"],\n", "                context_length=8192\n", "            ),\n", "            \"gemma2-9b-it\": ModelInfo(\n", "                name=\"gemma2-9b-it\",\n", "                provider=\"Groq\",\n", "                speed=\"fast\",\n", "                cost=\"low\",\n", "                quality=\"good\",\n", "                best_for=[\"quick responses\", \"simple tasks\", \"high volume\"],\n", "                context_length=8192\n", "            ),\n", "            \"llama3-70b-8192\": ModelInfo(\n", "                name=\"llama3-70b-8192\",\n", "                provider=\"Groq\",\n", "                speed=\"fast\",\n", "                cost=\"low\",\n", "                quality=\"very good\",\n", "                best_for=[\"code generation\", \"technical tasks\", \"reasoning\"],\n", "                context_length=8192\n", "            )\n", "        }\n", "    \n", "    def _initialize_models(self):\n", "        \"\"\"Initialize all available AI models\"\"\"\n", "        print(\"🔧 Initializing AI models...\")\n", "        \n", "        # OpenAI Models\n", "        for model_name in [\"gpt-3.5-turbo\", \"gpt-4\"]:\n", "            try:\n", "                self.models[model_name] = ChatOpenAI(\n", "                    model=model_name,\n", "                    temperature=0.7,\n", "                    max_tokens=1000\n", "                )\n", "                print(f\"  ✅ {model_name} initialized\")\n", "            except Exception as e:\n", "                print(f\"  ❌ {model_name} failed: {e}\")\n", "        \n", "        # Groq Models\n", "        for model_name in [\"gemma2-9b-it\", \"llama3-70b-8192\"]:\n", "            try:\n", "                self.models[model_name] = ChatGroq(\n", "                    model=model_name,\n", "                    temperature=0.7,\n", "                    max_tokens=1000\n", "                )\n", "                print(f\"  ✅ {model_name} initialized\")\n", "            except Exception as e:\n", "                print(f\"  ❌ {model_name} failed: {e}\")\n", "        \n", "        # Set default model\n", "        if self.models:\n", "            self.current_model = list(self.models.keys())[0]\n", "            print(f\"🎯 Default model: {self.current_model}\")\n", "        else:\n", "            print(\"❌ No models available! Check your API keys.\")\n", "\n", "# Create the complete model manager\n", "print(\"🚀 Creating Complete AI Model Manager...\")\n", "ai_manager = CompleteAIModelManager()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.2: Model Information and Comparison\n", "\n", "**What we're doing**: Displaying model characteristics in an easy-to-understand format\n", "\n", "**Why this helps**: Visual comparison makes it easy to choose the right model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add model information methods to our manager\n", "def get_model_comparison(self) -> pd.DataFrame:\n", "    \"\"\"Get a comparison table of all models\"\"\"\n", "    model_data = []\n", "    for model_id, info in self.model_info.items():\n", "        model_data.append({\n", "            \"Model\": info.name,\n", "            \"Provider\": info.provider,\n", "            \"Speed\": info.speed,\n", "            \"Cost\": info.cost,\n", "            \"Quality\": info.quality,\n", "            \"Context Length\": info.context_length,\n", "            \"Available\": \"✅\" if model_id in self.models else \"❌\",\n", "            \"Best For\": \", \".join(info.best_for[:2])\n", "        })\n", "    return pd.DataFrame(model_data)\n", "\n", "def get_available_models(self) -> List[str]:\n", "    \"\"\"Get list of available model names\"\"\"\n", "    return list(self.models.keys())\n", "\n", "def get_current_model(self) -> Optional[str]:\n", "    \"\"\"Get the currently active model name\"\"\"\n", "    return self.current_model\n", "\n", "# Add methods to our manager\n", "import types\n", "ai_manager.get_model_comparison = types.MethodType(get_model_comparison, ai_manager)\n", "ai_manager.get_available_models = types.MethodType(get_available_models, ai_manager)\n", "ai_manager.get_current_model = types.MethodType(get_current_model, ai_manager)\n", "\n", "# Display model comparison\n", "print(\"🤖 AI Models Comparison\")\n", "print(\"=\" * 60)\n", "comparison_df = ai_manager.get_model_comparison()\n", "print(comparison_df.to_string(index=False))\n", "\n", "print(\"\\n💡 Model Selection Guidelines:\")\n", "print(\"  🚀 Fast + Cheap: Use Groq models for simple tasks\")\n", "print(\"  ⚖️ Balanced: Use GPT-3.5-turbo for general purposes\")\n", "print(\"  🧠 Best Quality: Use GPT-4 for complex reasoning\")\n", "print(f\"\\n🎯 Current model: {ai_manager.get_current_model()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.3: Model Selection and Switching\n", "\n", "**What we're doing**: Adding intelligent model selection and switching capabilities\n", "\n", "**Why this is powerful**: Automatically choose the best model for each task\n", "\n", "**How it works**: We analyze the task and select the optimal model based on requirements"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add model selection and switching methods\n", "def set_model(self, model_name: str) -> bool:\n", "    \"\"\"Set the current active model\"\"\"\n", "    if model_name in self.models:\n", "        self.current_model = model_name\n", "        print(f\"✅ Switched to model: {model_name}\")\n", "        return True\n", "    else:\n", "        print(f\"❌ Model '{model_name}' not available\")\n", "        print(f\"Available models: {self.get_available_models()}\")\n", "        return False\n", "\n", "def recommend_model(self, task_type: str, priority: str = \"balanced\") -> str:\n", "    \"\"\"\n", "    Recommend the best model for a specific task\n", "    \n", "    Args:\n", "        task_type: Type of task (\"simple\", \"complex\", \"creative\", \"technical\")\n", "        priority: What to optimize for (\"speed\", \"quality\", \"cost\", \"balanced\")\n", "    \n", "    Returns:\n", "        Recommended model name\n", "    \"\"\"\n", "    recommendations = {\n", "        \"simple\": {\n", "            \"speed\": \"gemma2-9b-it\",\n", "            \"quality\": \"gpt-3.5-turbo\",\n", "            \"cost\": \"gemma2-9b-it\",\n", "            \"balanced\": \"gemma2-9b-it\"\n", "        },\n", "        \"complex\": {\n", "            \"speed\": \"llama3-70b-8192\",\n", "            \"quality\": \"gpt-4\",\n", "            \"cost\": \"llama3-70b-8192\",\n", "            \"balanced\": \"gpt-3.5-turbo\"\n", "        },\n", "        \"creative\": {\n", "            \"speed\": \"llama3-70b-8192\",\n", "            \"quality\": \"gpt-4\",\n", "            \"cost\": \"llama3-70b-8192\",\n", "            \"balanced\": \"gpt-3.5-turbo\"\n", "        },\n", "        \"technical\": {\n", "            \"speed\": \"llama3-70b-8192\",\n", "            \"quality\": \"gpt-4\",\n", "            \"cost\": \"llama3-70b-8192\",\n", "            \"balanced\": \"llama3-70b-8192\"\n", "        }\n", "    }\n", "    \n", "    recommended = recommendations.get(task_type, {}).get(priority, \"gpt-3.5-turbo\")\n", "    \n", "    # Check if recommended model is available\n", "    if recommended in self.models:\n", "        return recommended\n", "    else:\n", "        # Fallback to first available model\n", "        return self.get_available_models()[0] if self.models else None\n", "\n", "def auto_select_model(self, task_type: str, priority: str = \"balanced\") -> bool:\n", "    \"\"\"Automatically select and switch to the best model for a task\"\"\"\n", "    recommended = self.recommend_model(task_type, priority)\n", "    if recommended:\n", "        return self.set_model(recommended)\n", "    return False\n", "\n", "# Add methods to our manager\n", "ai_manager.set_model = types.MethodType(set_model, ai_manager)\n", "ai_manager.recommend_model = types.MethodType(recommend_model, ai_manager)\n", "ai_manager.auto_select_model = types.MethodType(auto_select_model, ai_manager)\n", "\n", "print(\"✅ Model selection methods added\")\n", "\n", "# Test model selection\n", "print(\"\\n🧪 Testing Model Selection...\")\n", "print(\"=\" * 40)\n", "\n", "# Test different scenarios\n", "scenarios = [\n", "    (\"simple\", \"speed\", \"Quick FAQ response\"),\n", "    (\"complex\", \"quality\", \"Detailed analysis\"),\n", "    (\"creative\", \"balanced\", \"Story writing\"),\n", "    (\"technical\", \"speed\", \"Code explanation\")\n", "]\n", "\n", "for task_type, priority, description in scenarios:\n", "    recommended = ai_manager.recommend_model(task_type, priority)\n", "    print(f\"📋 Task: {description}\")\n", "    print(f\"   Type: {task_type}, Priority: {priority}\")\n", "    print(f\"   Recommended: {recommended}\")\n", "    print()\n", "\n", "# Test auto-selection\n", "print(\"🔄 Testing auto-selection for creative task...\")\n", "success = ai_manager.auto_select_model(\"creative\", \"balanced\")\n", "if success:\n", "    print(f\"✅ Auto-selected: {ai_manager.get_current_model()}\")\n", "else:\n", "    print(\"❌ Auto-selection failed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.4: Performance Monitoring and Testing\n", "\n", "**What we're doing**: Adding comprehensive performance tracking\n", "\n", "**Why this matters**: Data-driven decisions about which models work best\n", "\n", "**What we track**: Response time, success rate, token usage, error patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add performance monitoring methods\n", "def invoke_with_monitoring(self, message: str, model_name: Optional[str] = None) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Invoke a model with comprehensive performance monitoring\n", "    \n", "    Args:\n", "        message: The message to send to the AI\n", "        model_name: Specific model to use (optional)\n", "    \n", "    Returns:\n", "        Dictionary with response and performance metrics\n", "    \"\"\"\n", "    # Use specified model or current model\n", "    model_to_use = model_name or self.current_model\n", "    \n", "    if not model_to_use or model_to_use not in self.models:\n", "        return {\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"error\": f\"Model '{model_to_use}' not available\",\n", "            \"response\": None,\n", "            \"metrics\": {}\n", "        }\n", "    \n", "    # Initialize performance stats for this model if needed\n", "    if model_to_use not in self.performance_stats:\n", "        self.performance_stats[model_to_use] = {\n", "            \"total_requests\": 0,\n", "            \"successful_requests\": 0,\n", "            \"total_response_time\": 0,\n", "            \"average_response_time\": 0,\n", "            \"last_used\": None,\n", "            \"errors\": []\n", "        }\n", "    \n", "    # Start timing\n", "    start_time = time.time()\n", "    \n", "    try:\n", "        # Get the model and invoke it\n", "        model = self.models[model_to_use]\n", "        response = model.invoke(message)\n", "        \n", "        # Calculate response time\n", "        response_time = time.time() - start_time\n", "        \n", "        # Update performance stats\n", "        stats = self.performance_stats[model_to_use]\n", "        stats[\"total_requests\"] += 1\n", "        stats[\"successful_requests\"] += 1\n", "        stats[\"total_response_time\"] += response_time\n", "        stats[\"average_response_time\"] = stats[\"total_response_time\"] / stats[\"total_requests\"]\n", "        stats[\"last_used\"] = datetime.now().isoformat()\n", "        \n", "        return {\n", "            \"success\": True,\n", "            \"response\": response.content,\n", "            \"model_used\": model_to_use,\n", "            \"metrics\": {\n", "                \"response_time\": response_time,\n", "                \"response_length\": len(response.content),\n", "                \"timestamp\": datetime.now().isoformat()\n", "            }\n", "        }\n", "        \n", "    except Exception as e:\n", "        # Update failure stats\n", "        response_time = time.time() - start_time\n", "        stats = self.performance_stats[model_to_use]\n", "        stats[\"total_requests\"] += 1\n", "        stats[\"total_response_time\"] += response_time\n", "        stats[\"average_response_time\"] = stats[\"total_response_time\"] / stats[\"total_requests\"]\n", "        stats[\"errors\"].append({\n", "            \"error\": str(e),\n", "            \"timestamp\": datetime.now().isoformat()\n", "        })\n", "        \n", "        return {\n", "            \"success\": <PERSON><PERSON><PERSON>,\n", "            \"error\": str(e),\n", "            \"response\": None,\n", "            \"model_used\": model_to_use,\n", "            \"metrics\": {\n", "                \"response_time\": response_time,\n", "                \"timestamp\": datetime.now().isoformat()\n", "            }\n", "        }\n", "\n", "def get_performance_report(self) -> str:\n", "    \"\"\"Generate a comprehensive performance report\"\"\"\n", "    if not self.performance_stats:\n", "        return \"No performance data available yet.\"\n", "    \n", "    report = \"📊 Model Performance Report\\n\"\n", "    report += \"=\" * 50 + \"\\n\\n\"\n", "    \n", "    for model_name, stats in self.performance_stats.items():\n", "        success_rate = (stats[\"successful_requests\"] / stats[\"total_requests\"] * 100) if stats[\"total_requests\"] > 0 else 0\n", "        \n", "        report += f\"🤖 {model_name}:\\n\"\n", "        report += f\"   Total Requests: {stats['total_requests']}\\n\"\n", "        report += f\"   Success Rate: {success_rate:.1f}%\\n\"\n", "        report += f\"   Avg Response Time: {stats['average_response_time']:.2f}s\\n\"\n", "        report += f\"   Last Used: {stats['last_used'] or 'Never'}\\n\"\n", "        report += f\"   Errors: {len(stats['errors'])}\\n\\n\"\n", "    \n", "    return report\n", "\n", "# Add performance monitoring methods\n", "ai_manager.invoke_with_monitoring = types.MethodType(invoke_with_monitoring, ai_manager)\n", "ai_manager.get_performance_report = types.MethodType(get_performance_report, ai_manager)\n", "\n", "print(\"✅ Performance monitoring methods added\")\n", "\n", "# Test performance monitoring\n", "print(\"\\n🧪 Testing Performance Monitoring...\")\n", "print(\"=\" * 40)\n", "\n", "# Test with different models\n", "test_message = \"What is artificial intelligence in one sentence?\"\n", "\n", "for model_name in ai_manager.get_available_models()[:2]:  # Test first 2 models\n", "    print(f\"\\n🤖 Testing {model_name}...\")\n", "    result = ai_manager.invoke_with_monitoring(test_message, model_name)\n", "    \n", "    if result[\"success\"]:\n", "        print(f\"✅ Success in {result['metrics']['response_time']:.2f}s\")\n", "        print(f\"📝 Response: {result['response'][:100]}...\")\n", "    else:\n", "        print(f\"❌ Failed: {result['error']}\")\n", "\n", "# Show performance report\n", "print(\"\\n\" + ai_manager.get_performance_report())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2.5: Model Comparison and Benchmarking\n", "\n", "**What we're doing**: Creating systematic model comparison tools\n", "\n", "**Why this is valuable**: Understand which models work best for different tasks\n", "\n", "**How it works**: Run the same prompts through different models and compare results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model comparison and benchmarking function\n", "def compare_models_on_tasks(ai_manager, test_prompts: List[str], models_to_test: Optional[List[str]] = None) -> Dict:\n", "    \"\"\"\n", "    Compare multiple models on the same set of prompts\n", "    \n", "    Args:\n", "        ai_manager: The AI model manager instance\n", "        test_prompts: List of prompts to test\n", "        models_to_test: Specific models to test (optional)\n", "    \n", "    Returns:\n", "        Comparison results with responses and metrics\n", "    \"\"\"\n", "    if models_to_test is None:\n", "        models_to_test = ai_manager.get_available_models()\n", "    \n", "    results = {\n", "        \"prompts\": test_prompts,\n", "        \"models\": models_to_test,\n", "        \"responses\": {},\n", "        \"metrics\": {},\n", "        \"summary\": {}\n", "    }\n", "    \n", "    print(f\"🔬 Comparing {len(models_to_test)} models on {len(test_prompts)} prompts...\")\n", "    print(\"=\" * 60)\n", "    \n", "    for i, prompt in enumerate(test_prompts):\n", "        print(f\"\\n📝 Prompt {i+1}: {prompt[:50]}...\")\n", "        results[\"responses\"][i] = {}\n", "        results[\"metrics\"][i] = {}\n", "        \n", "        for model_name in models_to_test:\n", "            print(f\"  🤖 Testing {model_name}...\", end=\" \")\n", "            \n", "            result = ai_manager.invoke_with_monitoring(prompt, model_name)\n", "            \n", "            if result[\"success\"]:\n", "                results[\"responses\"][i][model_name] = result[\"response\"]\n", "                results[\"metrics\"][i][model_name] = result[\"metrics\"]\n", "                print(f\"✅ {result['metrics']['response_time']:.2f}s\")\n", "            else:\n", "                results[\"responses\"][i][model_name] = f\"ERROR: {result['error']}\"\n", "                results[\"metrics\"][i][model_name] = {\"error\": result[\"error\"]}\n", "                print(\"❌ Failed\")\n", "    \n", "    # Generate summary statistics\n", "    for model_name in models_to_test:\n", "        response_times = []\n", "        success_count = 0\n", "        \n", "        for i in range(len(test_prompts)):\n", "            metrics = results[\"metrics\"][i].get(model_name, {})\n", "            if \"error\" not in metrics:\n", "                response_times.append(metrics.get(\"response_time\", 0))\n", "                success_count += 1\n", "        \n", "        results[\"summary\"][model_name] = {\n", "            \"success_rate\": (success_count / len(test_prompts)) * 100,\n", "            \"avg_response_time\": sum(response_times) / len(response_times) if response_times else 0,\n", "            \"total_tests\": len(test_prompts)\n", "        }\n", "    \n", "    return results\n", "\n", "# Define test prompts for comparison\n", "test_prompts = [\n", "    \"What is machine learning? Explain in one paragraph.\",\n", "    \"Write a haiku about programming.\",\n", "    \"List 3 benefits of using Python for data science.\"\n", "]\n", "\n", "# Run comparison (limit to available models)\n", "available_models = ai_manager.get_available_models()\n", "models_to_compare = available_models[:2] if len(available_models) >= 2 else available_models\n", "\n", "if models_to_compare:\n", "    print(\"🚀 Starting Model Comparison...\")\n", "    comparison_results = compare_models_on_tasks(ai_manager, test_prompts, models_to_compare)\n", "    \n", "    # Display comparison summary\n", "    print(\"\\n📊 Comparison Summary:\")\n", "    print(\"=\" * 50)\n", "    \n", "    for model_name, summary in comparison_results[\"summary\"].items():\n", "        print(f\"\\n🤖 {model_name}:\")\n", "        print(f\"   Success Rate: {summary['success_rate']:.1f}%\")\n", "        print(f\"   Avg Response Time: {summary['avg_response_time']:.2f}s\")\n", "        print(f\"   Tests Completed: {summary['total_tests']}\")\n", "    \n", "    # Show detailed responses for first prompt\n", "    print(f\"\\n📝 Detailed Responses for: '{test_prompts[0][:40]}...'\")\n", "    print(\"=\" * 60)\n", "    \n", "    for model_name in models_to_compare:\n", "        response = comparison_results[\"responses\"][0].get(model_name, \"No response\")\n", "        metrics = comparison_results[\"metrics\"][0].get(model_name, {})\n", "        \n", "        print(f\"\\n🤖 {model_name}:\")\n", "        if \"error\" not in metrics:\n", "            response_time = metrics.get(\"response_time\", 0)\n", "            response_length = metrics.get(\"response_length\", 0)\n", "            print(f\"   ⏱️ Time: {response_time:.2f}s | 📏 Length: {response_length} chars\")\n", "            print(f\"   📝 Response: {response[:150]}...\")\n", "        else:\n", "            print(f\"   ❌ Error: {metrics['error']}\")\n", "            \n", "else:\n", "    print(\"❌ No models available for comparison\")\n", "    print(\"💡 Make sure your API keys are set correctly in the .env file\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Step 2 Complete!\n", "\n", "### ✅ What We Accomplished\n", "1. **Complete Model Manager**: Built a professional class with all AI model management features\n", "2. **Model Comparison**: Created visual comparison tools to understand model characteristics\n", "3. **Intelligent Selection**: Implemented automatic model selection based on task requirements\n", "4. **Performance Monitoring**: Added comprehensive tracking for response times and success rates\n", "5. **Benchmarking Tools**: Built systematic model comparison and evaluation capabilities\n", "\n", "### 🔧 Technical Skills Gained\n", "- **Object-Oriented Design**: Professional class architecture with multiple responsibilities\n", "- **Strategy Pattern**: Dynamic model switching based on requirements\n", "- **Performance Monitoring**: Real-time tracking and analytics\n", "- **Error <PERSON>**: Robust failure management and recovery\n", "- **Data Analysis**: Comparing and evaluating model performance\n", "- **Method Injection**: Adding functionality to existing objects dynamically\n", "\n", "### 🚀 Ready for Next Step\n", "You now have a production-ready AI model management system that can:\n", "- Handle multiple AI providers (OpenAI, Groq)\n", "- Automatically select optimal models for tasks\n", "- Monitor performance and track usage\n", "- Compare models systematically\n", "- <PERSON><PERSON> errors gracefully\n", "\n", "**Next**: We'll move to **Step 3: Advanced Prompt Engineering** where we'll:\n", "- Build a comprehensive prompt template library\n", "- Create dynamic prompts that adapt to context\n", "- Implement prompt optimization and A/B testing\n", "- Add prompt versioning and management\n", "\n", "### 💡 Key Takeaways\n", "1. **Model Diversity**: Different models excel at different tasks - use this to your advantage\n", "2. **Performance Matters**: Always monitor what's working and what isn't\n", "3. **Automation**: Intelligent model selection saves time and improves results\n", "4. **Fallback Strategies**: Always have backup plans when models fail\n", "5. **Data-Driven Decisions**: Use metrics to choose the best approach\n", "\n", "### 🎯 Real-World Applications\n", "This model management system enables:\n", "- **Cost Optimization**: Use cheaper models for simple tasks, expensive ones for complex tasks\n", "- **Performance Optimization**: Route to fastest models when speed is critical\n", "- **Quality Optimization**: Use best models for important customer-facing tasks\n", "- **Reliability**: Automatic fallbacks ensure service continuity\n", "- **Analytics**: Understand usage patterns and optimize accordingly\n", "\n", "### 🏆 Professional Achievement\n", "You've built a system that many companies would pay thousands of dollars for! This demonstrates:\n", "- **System Architecture**: Designing scalable, maintainable systems\n", "- **Performance Engineering**: Building systems that monitor and optimize themselves\n", "- **Error Resilience**: Creating robust systems that handle failures gracefully\n", "- **Business Logic**: Understanding how to optimize for different business requirements\n", "\n", "Excellent work! You're ready to tackle advanced prompt engineering! 🎉"]}], "metadata": {"kernelspec": {"display_name": "ai_app_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}