{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🔧 Step 1: Environment Setup Review\n", "\n", "## 🎯 What We're Doing\n", "Setting up a complete, professional development environment for our AI application.\n", "\n", "## 🧠 Why This Step Matters\n", "- **Foundation**: Everything else depends on this working correctly\n", "- **Reproducibility**: Others can run your code with the same setup\n", "- **Professionalism**: Industry-standard development practices\n", "- **Debugging**: Easier to solve problems when environment is clean\n", "\n", "## 🔄 Connection to Previous Learning\n", "This builds on **Assignment 01** but adds more packages for our complete application."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1.1: Verify Conda Environment\n", "\n", "**What we're doing**: Checking if we have a proper conda environment\n", "\n", "**Why**: Conda environments keep our project isolated from other Python projects\n", "\n", "**Real-world analogy**: Like having a separate workshop for each project so tools don't get mixed up"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Environment Information:\n", "Python version: 3.10.18 (main, Jun  5 2025, 08:37:47) [Clang 14.0.6 ]\n", "Python executable: /opt/anaconda3/envs/agentic-2/bin/python\n", "Current working directory: /Users/<USER>/Desktop/Agentic-2.0/LangChain-Learning-Assignment/08-Complete-AI-Application\n", "✅ Conda environment: agentic-2\n", "\n", "💡 If you need to create a conda environment, run:\n", "   conda create -n ai_app_env python=3.10\n", "   conda activate ai_app_env\n"]}], "source": ["# Check current environment\n", "import sys\n", "import os\n", "\n", "print(\"🔍 Environment Information:\")\n", "print(f\"Python version: {sys.version}\")\n", "print(f\"Python executable: {sys.executable}\")\n", "print(f\"Current working directory: {os.getcwd()}\")\n", "\n", "# Check if we're in a conda environment\n", "conda_env = os.environ.get('CONDA_DEFAULT_ENV')\n", "if conda_env:\n", "    print(f\"✅ Conda environment: {conda_env}\")\n", "else:\n", "    print(\"⚠️ Not in a conda environment - consider creating one\")\n", "\n", "print(\"\\n💡 If you need to create a conda environment, run:\")\n", "print(\"   conda create -n ai_app_env python=3.10\")\n", "print(\"   conda activate ai_app_env\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1.2: Install Core Packages\n", "\n", "**What we're doing**: Installing all the Python packages we need\n", "\n", "**Why each package**:\n", "- **langchain**: Main AI framework (like the engine of a car)\n", "- **langchain-openai**: Connect to OpenAI models (like a phone line to OpenAI)\n", "- **langchain-groq**: Connect to Groq models (like a phone line to Groq)\n", "- **python-dotenv**: Load secret keys safely (like a secure vault)\n", "- **streamlit**: Create web interface (like building a website)\n", "\n", "**Installation process**: We'll install packages one by one and verify each works"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧪 Testing package installation function...\n", "✅ requests already installed and working\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Function to safely install and import packages\n", "def install_and_test_package(package_name, import_name=None):\n", "    \"\"\"\n", "    Install a package and test if it can be imported\n", "    \n", "    Args:\n", "        package_name: Name to install with pip\n", "        import_name: Name to use for import (if different)\n", "    \"\"\"\n", "    import subprocess\n", "    import importlib\n", "    \n", "    # Use package_name for import if import_name not specified\n", "    if import_name is None:\n", "        import_name = package_name\n", "    \n", "    try:\n", "        # Try to import first\n", "        importlib.import_module(import_name)\n", "        print(f\"✅ {package_name} already installed and working\")\n", "        return True\n", "    except ImportError:\n", "        print(f\"📦 Installing {package_name}...\")\n", "        try:\n", "            # Install the package\n", "            subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package_name])\n", "            \n", "            # Test import after installation\n", "            importlib.import_module(import_name)\n", "            print(f\"✅ {package_name} installed and working\")\n", "            return True\n", "        except Exception as e:\n", "            print(f\"❌ Failed to install {package_name}: {e}\")\n", "            return False\n", "\n", "# Test the function with a simple package\n", "print(\"🧪 Testing package installation function...\")\n", "install_and_test_package(\"requests\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📦 Installing Core LangChain Packages...\n", "==================================================\n", "✅ langchain already installed and working\n", "✅ langchain-openai already installed and working\n", "✅ langchain-groq already installed and working\n", "✅ langchain-core already installed and working\n", "✅ langchain-community already installed and working\n", "\n", "✅ Core LangChain packages installation complete!\n"]}], "source": ["# Install core LangChain packages\n", "print(\"📦 Installing Core LangChain Packages...\")\n", "print(\"=\" * 50)\n", "\n", "core_packages = [\n", "    (\"langchain\", \"langchain\"),\n", "    (\"langchain-openai\", \"langchain_openai\"),\n", "    (\"langchain-groq\", \"langchain_groq\"),\n", "    (\"langchain-core\", \"langchain_core\"),\n", "    (\"langchain-community\", \"langchain_community\")\n", "]\n", "\n", "for package, import_name in core_packages:\n", "    success = install_and_test_package(package, import_name)\n", "    if not success:\n", "        print(f\"⚠️ Warning: {package} installation failed\")\n", "\n", "print(\"\\n✅ Core LangChain packages installation complete!\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🛠️ Installing Utility Packages...\n", "==================================================\n", "✅ python-dotenv already installed and working\n", "✅ pydantic already installed and working\n", "✅ requests already installed and working\n", "📦 Installing pandas...\n", "Collecting pandas\n", "  Downloading pandas-2.3.0-cp310-cp310-macosx_11_0_arm64.whl.metadata (91 kB)\n", "Requirement already satisfied: numpy>=1.22.4 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from pandas) (2.2.6)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from pandas) (2.9.0.post0)\n", "Collecting pytz>=2020.1 (from pandas)\n", "  Using cached pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)\n", "Collecting tzdata>=2022.7 (from pandas)\n", "  Using cached tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Requirement already satisfied: six>=1.5 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Downloading pandas-2.3.0-cp310-cp310-macosx_11_0_arm64.whl (10.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.8/10.8 MB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m0:01\u001b[0m\n", "\u001b[?25hUsing cached pytz-2025.2-py2.py3-none-any.whl (509 kB)\n", "Using cached tzdata-2025.2-py2.py3-none-any.whl (347 kB)\n", "Installing collected packages: pytz, tzdata, pandas\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3/3\u001b[0m [pandas]2m2/3\u001b[0m [pandas]\n", "\u001b[1A\u001b[2KSuccessfully installed pandas-2.3.0 pytz-2025.2 tzdata-2025.2\n", "✅ pandas installed and working\n", "✅ numpy already installed and working\n", "\n", "✅ Utility packages installation complete!\n"]}], "source": ["# Install utility packages\n", "print(\"🛠️ Installing Utility Packages...\")\n", "print(\"=\" * 50)\n", "\n", "utility_packages = [\n", "    (\"python-dotenv\", \"dotenv\"),\n", "    (\"pydantic\", \"pydantic\"),\n", "    (\"requests\", \"requests\"),\n", "    (\"pandas\", \"pandas\"),\n", "    (\"numpy\", \"numpy\")\n", "]\n", "\n", "for package, import_name in utility_packages:\n", "    install_and_test_package(package, import_name)\n", "\n", "print(\"\\n✅ Utility packages installation complete!\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📄 Installing Document Processing Packages...\n", "==================================================\n", "✅ pypdf already installed and working\n", "📦 Installing python-docx...\n", "Collecting python-docx\n", "  Downloading python_docx-1.1.2-py3-none-any.whl.metadata (2.0 kB)\n", "Collecting lxml>=3.1.0 (from python-docx)\n", "  Downloading lxml-5.4.0-cp310-cp310-macosx_10_9_universal2.whl.metadata (3.5 kB)\n", "Requirement already satisfied: typing-extensions>=4.9.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from python-docx) (4.14.0)\n", "Downloading python_docx-1.1.2-py3-none-any.whl (244 kB)\n", "Downloading lxml-5.4.0-cp310-cp310-macosx_10_9_universal2.whl (8.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.1/8.1 MB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: lxml, python-docx\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2/2\u001b[0m [python-docx]\u001b[0m [python-docx]\n", "\u001b[1A\u001b[2KSuccessfully installed lxml-5.4.0 python-docx-1.1.2\n", "✅ python-docx installed and working\n", "✅ beautifulsoup4 already installed and working\n", "📦 Installing unstructured...\n", "Collecting unstructured\n", "  Downloading unstructured-0.17.2-py3-none-any.whl.metadata (24 kB)\n", "Collecting chardet (from unstructured)\n", "  Downloading chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)\n", "Collecting filetype (from unstructured)\n", "  Using cached filetype-1.2.0-py2.py3-none-any.whl.metadata (6.5 kB)\n", "Collecting python-magic (from unstructured)\n", "  Downloading python_magic-0.4.27-py2.py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: lxml in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured) (5.4.0)\n", "Collecting nltk (from unstructured)\n", "  Downloading nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)\n", "Requirement already satisfied: requests in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured) (2.32.3)\n", "Requirement already satisfied: beautifulsoup4 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured) (4.13.4)\n", "Collecting emoji (from unstructured)\n", "  Downloading emoji-2.14.1-py3-none-any.whl.metadata (5.7 kB)\n", "Requirement already satisfied: dataclasses-json in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured) (0.6.7)\n", "Collecting python-iso639 (from unstructured)\n", "  Downloading python_iso639-2025.2.18-py3-none-any.whl.metadata (14 kB)\n", "Collecting langdetect (from unstructured)\n", "  Downloading langdetect-1.0.9.tar.gz (981 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m981.5/981.5 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py): started\n", "  Preparing metadata (setup.py): finished with status 'done'\n", "Requirement already satisfied: numpy in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured) (2.2.6)\n", "Collecting rapidfuzz (from unstructured)\n", "  Downloading rapidfuzz-3.13.0-cp310-cp310-macosx_11_0_arm64.whl.metadata (12 kB)\n", "Collecting backoff (from unstructured)\n", "  Using cached backoff-2.2.1-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: typing-extensions in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured) (4.14.0)\n", "Collecting unstructured-client (from unstructured)\n", "  Downloading unstructured_client-0.36.0-py3-none-any.whl.metadata (21 kB)\n", "Collecting wrapt (from unstructured)\n", "  Using cached wrapt-1.17.2-cp310-cp310-macosx_11_0_arm64.whl.metadata (6.4 kB)\n", "Requirement already satisfied: tqdm in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured) (4.67.1)\n", "Requirement already satisfied: psutil in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured) (7.0.0)\n", "Collecting python-oxmsg (from unstructured)\n", "  Downloading python_oxmsg-0.0.2-py3-none-any.whl.metadata (5.0 kB)\n", "Collecting html5lib (from unstructured)\n", "  Using cached html5lib-1.1-py2.py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: soupsieve>1.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from beautifulsoup4->unstructured) (2.7)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from dataclasses-json->unstructured) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from dataclasses-json->unstructured) (0.9.0)\n", "Requirement already satisfied: packaging>=17.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->unstructured) (24.2)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json->unstructured) (1.1.0)\n", "Requirement already satisfied: six>=1.9 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from html5lib->unstructured) (1.17.0)\n", "Collecting webencodings (from html5lib->unstructured)\n", "  Using cached webencodings-0.5.1-py2.py3-none-any.whl.metadata (2.1 kB)\n", "Collecting click (from nltk->unstructured)\n", "  Downloading click-8.2.1-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting joblib (from nltk->unstructured)\n", "  Downloading joblib-1.5.1-py3-none-any.whl.metadata (5.6 kB)\n", "Requirement already satisfied: regex>=2021.8.3 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from nltk->unstructured) (2024.11.6)\n", "Collecting olefile (from python-oxmsg->unstructured)\n", "  Downloading olefile-0.47-py2.py3-none-any.whl.metadata (9.7 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from requests->unstructured) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from requests->unstructured) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from requests->unstructured) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from requests->unstructured) (2025.4.26)\n", "Collecting aiofiles>=24.1.0 (from unstructured-client->unstructured)\n", "  Downloading aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)\n", "Collecting cryptography>=3.1 (from unstructured-client->unstructured)\n", "  Downloading cryptography-45.0.3-cp37-abi3-macosx_10_9_universal2.whl.metadata (5.7 kB)\n", "Requirement already satisfied: httpx>=0.27.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured-client->unstructured) (0.28.1)\n", "Requirement already satisfied: nest-asyncio>=1.6.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured-client->unstructured) (1.6.0)\n", "Requirement already satisfied: pydantic>=2.11.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured-client->unstructured) (2.11.5)\n", "Requirement already satisfied: pypdf>=4.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured-client->unstructured) (5.6.0)\n", "Requirement already satisfied: requests-toolbelt>=1.0.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from unstructured-client->unstructured) (1.0.0)\n", "Collecting cffi>=1.14 (from cryptography>=3.1->unstructured-client->unstructured)\n", "  Using cached cffi-1.17.1-cp310-cp310-macosx_11_0_arm64.whl.metadata (1.5 kB)\n", "Collecting pycparser (from cffi>=1.14->cryptography>=3.1->unstructured-client->unstructured)\n", "  Using cached pycparser-2.22-py3-none-any.whl.metadata (943 bytes)\n", "Requirement already satisfied: anyio in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from httpx>=0.27.0->unstructured-client->unstructured) (4.9.0)\n", "Requirement already satisfied: httpcore==1.* in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from httpx>=0.27.0->unstructured-client->unstructured) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from httpcore==1.*->httpx>=0.27.0->unstructured-client->unstructured) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from pydantic>=2.11.2->unstructured-client->unstructured) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from pydantic>=2.11.2->unstructured-client->unstructured) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from pydantic>=2.11.2->unstructured-client->unstructured) (0.4.1)\n", "Requirement already satisfied: exceptiongroup>=1.0.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from anyio->httpx>=0.27.0->unstructured-client->unstructured) (1.3.0)\n", "Requirement already satisfied: sniffio>=1.1 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from anyio->httpx>=0.27.0->unstructured-client->unstructured) (1.3.1)\n", "Downloading unstructured-0.17.2-py3-none-any.whl (1.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hUsing cached backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Downloading chardet-5.2.0-py3-none-any.whl (199 kB)\n", "Downloading emoji-2.14.1-py3-none-any.whl (590 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m590.6/590.6 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hUsing cached filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Using cached html5lib-1.1-py2.py3-none-any.whl (112 kB)\n", "Downloading nltk-3.9.1-py3-none-any.whl (1.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.5/1.5 MB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading click-8.2.1-py3-none-any.whl (102 kB)\n", "Downloading joblib-1.5.1-py3-none-any.whl (307 kB)\n", "Downloading python_iso639-2025.2.18-py3-none-any.whl (167 kB)\n", "Downloading python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\n", "Downloading python_oxmsg-0.0.2-py3-none-any.whl (31 kB)\n", "Downloading olefile-0.47-py2.py3-none-any.whl (114 kB)\n", "Downloading rapidfuzz-3.13.0-cp310-cp310-macosx_11_0_arm64.whl (1.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading unstructured_client-0.36.0-py3-none-any.whl (195 kB)\n", "Downloading aiofiles-24.1.0-py3-none-any.whl (15 kB)\n", "Downloading cryptography-45.0.3-cp37-abi3-macosx_10_9_universal2.whl (7.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.0/7.0 MB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hUsing cached cffi-1.17.1-cp310-cp310-macosx_11_0_arm64.whl (178 kB)\n", "Using cached pycparser-2.22-py3-none-any.whl (117 kB)\n", "Using cached webencodings-0.5.1-py2.py3-none-any.whl (11 kB)\n", "Using cached wrapt-1.17.2-cp310-cp310-macosx_11_0_arm64.whl (38 kB)\n", "Building wheels for collected packages: langdetect\n", "  Building wheel for langdetect (setup.py): started\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33m  DEPRECATION: Building 'langdetect' using the legacy setup.py bdist_wheel mechanism, which will be removed in a future version. pip 25.3 will enforce this behaviour change. A possible replacement is to use the standardized build interface by setting the `--use-pep517` option, (possibly combined with `--no-build-isolation`), or adding a `pyproject.toml` file to the source tree of 'langdetect'. Discussion can be found at https://github.com/pypa/pip/issues/6334\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["  Building wheel for langdetect (setup.py): finished with status 'done'\n", "  Created wheel for langdetect: filename=langdetect-1.0.9-py3-none-any.whl size=993332 sha256=812702a45525c38fb7f3a414e453ebcf9921e2038ff6383d77d27571e7040d5d\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/95/03/7d/59ea870c70ce4e5a370638b5462a7711ab78fba2f655d05106\n", "Successfully built langdetect\n", "Installing collected packages: webencodings, filetype, wrapt, rapidfuzz, python-magic, python-iso639, pycparser, olefile, langdetect, joblib, html5lib, emoji, click, chardet, backoff, aiofiles, python-oxmsg, nltk, cffi, cryptography, unstructured-client, unstructured\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m22/22\u001b[0m [unstructured][0m [unstructured]client]\n", "\u001b[1A\u001b[2KSuccessfully installed aiofiles-24.1.0 backoff-2.2.1 cffi-1.17.1 chardet-5.2.0 click-8.2.1 cryptography-45.0.3 emoji-2.14.1 filetype-1.2.0 html5lib-1.1 joblib-1.5.1 langdetect-1.0.9 nltk-3.9.1 olefile-0.47 pycparser-2.22 python-iso639-2025.2.18 python-magic-0.4.27 python-oxmsg-0.0.2 rapidfuzz-3.13.0 unstructured-0.17.2 unstructured-client-0.36.0 webencodings-0.5.1 wrapt-1.17.2\n", "✅ unstructured installed and working\n", "\n", "✅ Document processing packages installation complete!\n"]}], "source": ["# Install document processing packages\n", "print(\"📄 Installing Document Processing Packages...\")\n", "print(\"=\" * 50)\n", "\n", "document_packages = [\n", "    (\"pypdf\", \"pypdf\"),\n", "    (\"python-docx\", \"docx\"),\n", "    (\"beautifulsoup4\", \"bs4\"),\n", "    (\"unstructured\", \"unstructured\")\n", "]\n", "\n", "for package, import_name in document_packages:\n", "    install_and_test_package(package, import_name)\n", "\n", "print(\"\\n✅ Document processing packages installation complete!\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Installing Vector Search Packages...\n", "==================================================\n", "📦 Installing chromadb...\n", "Collecting chromadb\n", "  Downloading chromadb-1.0.12-cp39-abi3-macosx_11_0_arm64.whl.metadata (6.9 kB)\n", "Collecting build>=1.0.3 (from chromadb)\n", "  Downloading build-1.2.2.post1-py3-none-any.whl.metadata (6.5 kB)\n", "Requirement already satisfied: pydantic>=1.9 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from chromadb) (2.11.5)\n", "Collecting fastapi==0.115.9 (from chromadb)\n", "  Downloading fastapi-0.115.9-py3-none-any.whl.metadata (27 kB)\n", "Collecting uvicorn>=0.18.3 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading uvicorn-0.34.3-py3-none-any.whl.metadata (6.5 kB)\n", "Requirement already satisfied: numpy>=1.22.5 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from chromadb) (2.2.6)\n", "Collecting posthog>=2.4.0 (from chromadb)\n", "  Downloading posthog-4.3.2-py2.py3-none-any.whl.metadata (5.5 kB)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from chromadb) (4.14.0)\n", "Collecting onnxruntime>=1.14.1 (from chromadb)\n", "  Downloading onnxruntime-1.22.0-cp310-cp310-macosx_13_0_universal2.whl.metadata (4.5 kB)\n", "Collecting opentelemetry-api>=1.2.0 (from chromadb)\n", "  Downloading opentelemetry_api-1.34.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-grpc>=1.2.0 (from chromadb)\n", "  Downloading opentelemetry_exporter_otlp_proto_grpc-1.34.0-py3-none-any.whl.metadata (2.4 kB)\n", "Collecting opentelemetry-instrumentation-fastapi>=0.41b0 (from chromadb)\n", "  Downloading opentelemetry_instrumentation_fastapi-0.55b0-py3-none-any.whl.metadata (2.2 kB)\n", "Collecting opentelemetry-sdk>=1.2.0 (from chromadb)\n", "  Downloading opentelemetry_sdk-1.34.0-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting tokenizers>=0.13.2 (from chromadb)\n", "  Downloading tokenizers-0.21.1-cp39-abi3-macosx_11_0_arm64.whl.metadata (6.8 kB)\n", "Collecting pypika>=0.48.9 (from chromadb)\n", "  Downloading PyPika-0.48.9.tar.gz (67 kB)\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: tqdm>=4.65.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from chromadb) (4.67.1)\n", "Collecting overrides>=7.3.1 (from chromadb)\n", "  Using cached overrides-7.7.0-py3-none-any.whl.metadata (5.8 kB)\n", "Collecting importlib-resources (from chromadb)\n", "  Downloading importlib_resources-6.5.2-py3-none-any.whl.metadata (3.9 kB)\n", "Collecting grpcio>=1.58.0 (from chromadb)\n", "  Downloading grpcio-1.72.1-cp310-cp310-macosx_11_0_universal2.whl.metadata (3.8 kB)\n", "Collecting bcrypt>=4.0.1 (from chromadb)\n", "  Downloading bcrypt-4.3.0-cp39-abi3-macosx_10_12_universal2.whl.metadata (10 kB)\n", "Collecting typer>=0.9.0 (from chromadb)\n", "  Downloading typer-0.16.0-py3-none-any.whl.metadata (15 kB)\n", "Collecting kubernetes>=28.1.0 (from chromadb)\n", "  Downloading kubernetes-32.0.1-py2.py3-none-any.whl.metadata (1.7 kB)\n", "Requirement already satisfied: tenacity>=8.2.3 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from chromadb) (9.1.2)\n", "Requirement already satisfied: pyyaml>=6.0.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from chromadb) (6.0.2)\n", "Collecting mmh3>=4.0.1 (from chromadb)\n", "  Downloading mmh3-5.1.0-cp310-cp310-macosx_11_0_arm64.whl.metadata (16 kB)\n", "Requirement already satisfied: orjson>=3.9.12 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from chromadb) (3.10.18)\n", "Requirement already satisfied: httpx>=0.27.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from chromadb) (0.28.1)\n", "Collecting rich>=10.11.0 (from chromadb)\n", "  Downloading rich-14.0.0-py3-none-any.whl.metadata (18 kB)\n", "Collecting jsonschema>=4.19.0 (from chromadb)\n", "  Using cached jsonschema-4.24.0-py3-none-any.whl.metadata (7.8 kB)\n", "Collecting starlette<0.46.0,>=0.40.0 (from fastapi==0.115.9->chromadb)\n", "  Downloading starlette-0.45.3-py3-none-any.whl.metadata (6.3 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from pydantic>=1.9->chromadb) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from pydantic>=1.9->chromadb) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from pydantic>=1.9->chromadb) (0.4.1)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from starlette<0.46.0,>=0.40.0->fastapi==0.115.9->chromadb) (4.9.0)\n", "Requirement already satisfied: exceptiongroup>=1.0.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi==0.115.9->chromadb) (1.3.0)\n", "Requirement already satisfied: idna>=2.8 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi==0.115.9->chromadb) (3.10)\n", "Requirement already satisfied: sniffio>=1.1 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi==0.115.9->chromadb) (1.3.1)\n", "Requirement already satisfied: packaging>=19.1 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from build>=1.0.3->chromadb) (24.2)\n", "Collecting pyproject_hooks (from build>=1.0.3->chromadb)\n", "  Downloading pyproject_hooks-1.2.0-py3-none-any.whl.metadata (1.3 kB)\n", "Collecting tomli>=1.1.0 (from build>=1.0.3->chromadb)\n", "  Using cached tomli-2.2.1-py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: certifi in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from httpx>=0.27.0->chromadb) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from httpx>=0.27.0->chromadb) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from httpcore==1.*->httpx>=0.27.0->chromadb) (0.16.0)\n", "Requirement already satisfied: attrs>=22.2.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from jsonschema>=4.19.0->chromadb) (25.3.0)\n", "Collecting jsonschema-specifications>=2023.03.6 (from jsonschema>=4.19.0->chromadb)\n", "  Using cached jsonschema_specifications-2025.4.1-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting referencing>=0.28.4 (from jsonschema>=4.19.0->chromadb)\n", "  Using cached referencing-0.36.2-py3-none-any.whl.metadata (2.8 kB)\n", "Collecting rpds-py>=0.7.1 (from jsonschema>=4.19.0->chromadb)\n", "  Using cached rpds_py-0.25.1-cp310-cp310-macosx_11_0_arm64.whl.metadata (4.1 kB)\n", "Requirement already satisfied: six>=1.9.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from kubernetes>=28.1.0->chromadb) (1.17.0)\n", "Requirement already satisfied: python-dateutil>=2.5.3 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from kubernetes>=28.1.0->chromadb) (2.9.0.post0)\n", "Collecting google-auth>=1.0.1 (from kubernetes>=28.1.0->chromadb)\n", "  Downloading google_auth-2.40.3-py2.py3-none-any.whl.metadata (6.2 kB)\n", "Collecting websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 (from kubernetes>=28.1.0->chromadb)\n", "  Using cached websocket_client-1.8.0-py3-none-any.whl.metadata (8.0 kB)\n", "Requirement already satisfied: requests in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from kubernetes>=28.1.0->chromadb) (2.32.3)\n", "Collecting requests-oauthlib (from kubernetes>=28.1.0->chromadb)\n", "  Downloading requests_oauthlib-2.0.0-py2.py3-none-any.whl.metadata (11 kB)\n", "Collecting oauthlib>=3.2.2 (from kubernetes>=28.1.0->chromadb)\n", "  Downloading oauthlib-3.2.2-py3-none-any.whl.metadata (7.5 kB)\n", "Requirement already satisfied: urllib3>=1.24.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from kubernetes>=28.1.0->chromadb) (2.4.0)\n", "Collecting durationpy>=0.7 (from kubernetes>=28.1.0->chromadb)\n", "  Downloading durationpy-0.10-py3-none-any.whl.metadata (340 bytes)\n", "Collecting cachetools<6.0,>=2.0.0 (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb)\n", "  Using cached cachetools-5.5.2-py3-none-any.whl.metadata (5.4 kB)\n", "Collecting pyasn1-modules>=0.2.1 (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb)\n", "  Using cached pyasn1_modules-0.4.2-py3-none-any.whl.metadata (3.5 kB)\n", "Collecting rsa<5,>=3.1.4 (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb)\n", "  Downloading rsa-4.9.1-py3-none-any.whl.metadata (5.6 kB)\n", "Collecting pyasn1>=0.1.3 (from rsa<5,>=3.1.4->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb)\n", "  Using cached pyasn1-0.6.1-py3-none-any.whl.metadata (8.4 kB)\n", "Collecting coloredlogs (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading coloredlogs-15.0.1-py2.py3-none-any.whl.metadata (12 kB)\n", "Collecting flatbuffers (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading flatbuffers-25.2.10-py2.py3-none-any.whl.metadata (875 bytes)\n", "Collecting protobuf (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading protobuf-6.31.1-cp39-abi3-macosx_10_9_universal2.whl.metadata (593 bytes)\n", "Collecting sympy (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading sympy-1.14.0-py3-none-any.whl.metadata (12 kB)\n", "Collecting importlib-metadata<8.8.0,>=6.0 (from opentelemetry-api>=1.2.0->chromadb)\n", "  Downloading importlib_metadata-8.7.0-py3-none-any.whl.metadata (4.8 kB)\n", "Collecting zipp>=3.20 (from importlib-metadata<8.8.0,>=6.0->opentelemetry-api>=1.2.0->chromadb)\n", "  Downloading zipp-3.22.0-py3-none-any.whl.metadata (3.6 kB)\n", "Collecting googleapis-common-protos~=1.52 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb)\n", "  Using cached googleapis_common_protos-1.70.0-py3-none-any.whl.metadata (9.3 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-common==1.34.0 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb)\n", "  Downloading opentelemetry_exporter_otlp_proto_common-1.34.0-py3-none-any.whl.metadata (1.9 kB)\n", "Collecting opentelemetry-proto==1.34.0 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb)\n", "  Downloading opentelemetry_proto-1.34.0-py3-none-any.whl.metadata (2.4 kB)\n", "Collecting protobuf (from onnxruntime>=1.14.1->chromadb)\n", "  Downloading protobuf-5.29.5-cp38-abi3-macosx_10_9_universal2.whl.metadata (592 bytes)\n", "Collecting opentelemetry-semantic-conventions==0.55b0 (from opentelemetry-sdk>=1.2.0->chromadb)\n", "  Downloading opentelemetry_semantic_conventions-0.55b0-py3-none-any.whl.metadata (2.5 kB)\n", "Collecting opentelemetry-instrumentation-asgi==0.55b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_instrumentation_asgi-0.55b0-py3-none-any.whl.metadata (2.0 kB)\n", "Collecting opentelemetry-instrumentation==0.55b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_instrumentation-0.55b0-py3-none-any.whl.metadata (6.7 kB)\n", "Collecting opentelemetry-util-http==0.55b0 (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading opentelemetry_util_http-0.55b0-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: wrapt<2.0.0,>=1.0.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from opentelemetry-instrumentation==0.55b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb) (1.17.2)\n", "Collecting asgiref~=3.0 (from opentelemetry-instrumentation-asgi==0.55b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb)\n", "  Downloading asgiref-3.8.1-py3-none-any.whl.metadata (9.3 kB)\n", "Requirement already satisfied: backoff>=1.10.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from posthog>=2.4.0->chromadb) (2.2.1)\n", "Requirement already satisfied: distro>=1.5.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from posthog>=2.4.0->chromadb) (1.9.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from requests->kubernetes>=28.1.0->chromadb) (3.4.2)\n", "Collecting markdown-it-py>=2.2.0 (from rich>=10.11.0->chromadb)\n", "  Using cached markdown_it_py-3.0.0-py3-none-any.whl.metadata (6.9 kB)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from rich>=10.11.0->chromadb) (2.19.1)\n", "Collecting mdurl~=0.1 (from markdown-it-py>=2.2.0->rich>=10.11.0->chromadb)\n", "  Using cached mdurl-0.1.2-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting huggingface-hub<1.0,>=0.16.4 (from tokenizers>=0.13.2->chromadb)\n", "  Downloading huggingface_hub-0.32.4-py3-none-any.whl.metadata (14 kB)\n", "Collecting filelock (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb)\n", "  Using cached filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting fsspec>=2023.5.0 (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb)\n", "  Downloading fsspec-2025.5.1-py3-none-any.whl.metadata (11 kB)\n", "Collecting hf-xet<2.0.0,>=1.1.2 (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb)\n", "  Downloading hf_xet-1.1.3-cp37-abi3-macosx_11_0_arm64.whl.metadata (879 bytes)\n", "Requirement already satisfied: click>=8.0.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from typer>=0.9.0->chromadb) (8.2.1)\n", "Collecting shellingham>=1.3.0 (from typer>=0.9.0->chromadb)\n", "  Using cached shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)\n", "Collecting httptools>=0.6.3 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading httptools-0.6.4-cp310-cp310-macosx_11_0_arm64.whl.metadata (3.6 kB)\n", "Requirement already satisfied: python-dotenv>=0.13 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (1.1.0)\n", "Collecting uvloop>=0.15.1 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading uvloop-0.21.0-cp310-cp310-macosx_10_9_universal2.whl.metadata (4.9 kB)\n", "Collecting watchfiles>=0.13 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading watchfiles-1.0.5-cp310-cp310-macosx_11_0_arm64.whl.metadata (4.9 kB)\n", "Collecting websockets>=10.4 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading websockets-15.0.1-cp310-cp310-macosx_11_0_arm64.whl.metadata (6.8 kB)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.14.1->chromadb)\n", "  Downloading humanfriendly-10.0-py2.py3-none-any.whl.metadata (9.2 kB)\n", "Collecting mpmath<1.4,>=1.1.0 (from sympy->onnxruntime>=1.14.1->chromadb)\n", "  Downloading mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)\n", "Downloading chromadb-1.0.12-cp39-abi3-macosx_11_0_arm64.whl (17.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m17.9/17.9 MB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading fastapi-0.115.9-py3-none-any.whl (94 kB)\n", "Downloading starlette-0.45.3-py3-none-any.whl (71 kB)\n", "Downloading bcrypt-4.3.0-cp39-abi3-macosx_10_12_universal2.whl (498 kB)\n", "Downloading build-1.2.2.post1-py3-none-any.whl (22 kB)\n", "Downloading grpcio-1.72.1-cp310-cp310-macosx_11_0_universal2.whl (10.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.3/10.3 MB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hUsing cached jsonschema-4.24.0-py3-none-any.whl (88 kB)\n", "Using cached jsonschema_specifications-2025.4.1-py3-none-any.whl (18 kB)\n", "Downloading kubernetes-32.0.1-py2.py3-none-any.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m7.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading durationpy-0.10-py3-none-any.whl (3.9 kB)\n", "Downloading google_auth-2.40.3-py2.py3-none-any.whl (216 kB)\n", "Using cached cachetools-5.5.2-py3-none-any.whl (10 kB)\n", "Downloading rsa-4.9.1-py3-none-any.whl (34 kB)\n", "Downloading mmh3-5.1.0-cp310-cp310-macosx_11_0_arm64.whl (40 kB)\n", "Downloading oauthlib-3.2.2-py3-none-any.whl (151 kB)\n", "Downloading onnxruntime-1.22.0-cp310-cp310-macosx_13_0_universal2.whl (34.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m34.3/34.3 MB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_api-1.34.0-py3-none-any.whl (65 kB)\n", "Downloading importlib_metadata-8.7.0-py3-none-any.whl (27 kB)\n", "Downloading opentelemetry_exporter_otlp_proto_grpc-1.34.0-py3-none-any.whl (18 kB)\n", "Downloading opentelemetry_exporter_otlp_proto_common-1.34.0-py3-none-any.whl (18 kB)\n", "Downloading opentelemetry_proto-1.34.0-py3-none-any.whl (55 kB)\n", "Using cached googleapis_common_protos-1.70.0-py3-none-any.whl (294 kB)\n", "Downloading opentelemetry_sdk-1.34.0-py3-none-any.whl (118 kB)\n", "Downloading opentelemetry_semantic_conventions-0.55b0-py3-none-any.whl (196 kB)\n", "Downloading protobuf-5.29.5-cp38-abi3-macosx_10_9_universal2.whl (418 kB)\n", "Downloading opentelemetry_instrumentation_fastapi-0.55b0-py3-none-any.whl (12 kB)\n", "Downloading opentelemetry_instrumentation-0.55b0-py3-none-any.whl (31 kB)\n", "Downloading opentelemetry_instrumentation_asgi-0.55b0-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_util_http-0.55b0-py3-none-any.whl (7.3 kB)\n", "Downloading asgiref-3.8.1-py3-none-any.whl (23 kB)\n", "Using cached overrides-7.7.0-py3-none-any.whl (17 kB)\n", "Downloading posthog-4.3.2-py2.py3-none-any.whl (102 kB)\n", "Using cached pyasn1-0.6.1-py3-none-any.whl (83 kB)\n", "Using cached pyasn1_modules-0.4.2-py3-none-any.whl (181 kB)\n", "Using cached referencing-0.36.2-py3-none-any.whl (26 kB)\n", "Downloading rich-14.0.0-py3-none-any.whl (243 kB)\n", "Using cached markdown_it_py-3.0.0-py3-none-any.whl (87 kB)\n", "Using cached mdurl-0.1.2-py3-none-any.whl (10.0 kB)\n", "Using cached rpds_py-0.25.1-cp310-cp310-macosx_11_0_arm64.whl (358 kB)\n", "Downloading tokenizers-0.21.1-cp39-abi3-macosx_11_0_arm64.whl (2.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.7/2.7 MB\u001b[0m \u001b[31m7.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading huggingface_hub-0.32.4-py3-none-any.whl (512 kB)\n", "Downloading hf_xet-1.1.3-cp37-abi3-macosx_11_0_arm64.whl (2.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m7.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading fsspec-2025.5.1-py3-none-any.whl (199 kB)\n", "Using cached tomli-2.2.1-py3-none-any.whl (14 kB)\n", "Downloading typer-0.16.0-py3-none-any.whl (46 kB)\n", "Using cached shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)\n", "Downloading uvicorn-0.34.3-py3-none-any.whl (62 kB)\n", "Downloading httptools-0.6.4-cp310-cp310-macosx_11_0_arm64.whl (103 kB)\n", "Downloading uvloop-0.21.0-cp310-cp310-macosx_10_9_universal2.whl (1.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.4/1.4 MB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading watchfiles-1.0.5-cp310-cp310-macosx_11_0_arm64.whl (395 kB)\n", "Using cached websocket_client-1.8.0-py3-none-any.whl (58 kB)\n", "Downloading websockets-15.0.1-cp310-cp310-macosx_11_0_arm64.whl (173 kB)\n", "Downloading zipp-3.22.0-py3-none-any.whl (9.8 kB)\n", "Downloading coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "Downloading humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "Using cached filelock-3.18.0-py3-none-any.whl (16 kB)\n", "Downloading flatbuffers-25.2.10-py2.py3-none-any.whl (30 kB)\n", "Downloading importlib_resources-6.5.2-py3-none-any.whl (37 kB)\n", "Downloading pyproject_hooks-1.2.0-py3-none-any.whl (10 kB)\n", "Downloading requests_oauthlib-2.0.0-py2.py3-none-any.whl (24 kB)\n", "Downloading sympy-1.14.0-py3-none-any.whl (6.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.3/6.3 MB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading mpmath-1.3.0-py3-none-any.whl (536 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m536.2/536.2 kB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hBuilding wheels for collected packages: pypika\n", "  Building wheel for pypika (pyproject.toml): started\n", "  Building wheel for pypika (pyproject.toml): finished with status 'done'\n", "  Created wheel for pypika: filename=pypika-0.48.9-py2.py3-none-any.whl size=53803 sha256=3abef7b4c7668caeb314426991f7df3d5fe363523f79ec337aa65a6737f16d46\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/e1/26/51/d0bffb3d2fd82256676d7ad3003faea3bd6dddc9577af665f4\n", "Successfully built pypika\n", "Installing collected packages: pypika, mpmath, flatbuffers, durationpy, zipp, websockets, websocket-client, uvloop, uvicorn, tomli, sympy, shellingham, rpds-py, pyproject_hooks, pyasn1, protobuf, overrides, opentelemetry-util-http, oauthlib, mmh3, mdurl, importlib-resources, humanfriendly, httptools, hf-xet, grpcio, fsspec, filelock, cachetools, bcrypt, asgiref, rsa, requests-oauthlib, referencing, pyasn1-modules, posthog, opentelemetry-proto, markdown-it-py, importlib-metadata, huggingface-hub, googleapis-common-protos, coloredlogs, build, watchfiles, tokenizers, starlette, rich, opentelemetry-exporter-otlp-proto-common, opentelemetry-api, onnxruntime, jsonschema-specifications, google-auth, typer, opentelemetry-semantic-conventions, kubernetes, jsonschema, fastapi, opentelemetry-sdk, opentelemetry-instrumentation, opentelemetry-instrumentation-asgi, opentelemetry-exporter-otlp-proto-grpc, opentelemetry-instrumentation-fastapi, chromadb\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m63/63\u001b[0m [chromadb]chromadb]lemetry-instrumentation-asgi]\n", "\u001b[1A\u001b[2KSuccessfully installed asgiref-3.8.1 bcrypt-4.3.0 build-1.2.2.post1 cachetools-5.5.2 chromadb-1.0.12 coloredlogs-15.0.1 durationpy-0.10 fastapi-0.115.9 filelock-3.18.0 flatbuffers-25.2.10 fsspec-2025.5.1 google-auth-2.40.3 googleapis-common-protos-1.70.0 grpcio-1.72.1 hf-xet-1.1.3 httptools-0.6.4 huggingface-hub-0.32.4 humanfriendly-10.0 importlib-metadata-8.7.0 importlib-resources-6.5.2 jsonschema-4.24.0 jsonschema-specifications-2025.4.1 kubernetes-32.0.1 markdown-it-py-3.0.0 mdurl-0.1.2 mmh3-5.1.0 mpmath-1.3.0 oauthlib-3.2.2 onnxruntime-1.22.0 opentelemetry-api-1.34.0 opentelemetry-exporter-otlp-proto-common-1.34.0 opentelemetry-exporter-otlp-proto-grpc-1.34.0 opentelemetry-instrumentation-0.55b0 opentelemetry-instrumentation-asgi-0.55b0 opentelemetry-instrumentation-fastapi-0.55b0 opentelemetry-proto-1.34.0 opentelemetry-sdk-1.34.0 opentelemetry-semantic-conventions-0.55b0 opentelemetry-util-http-0.55b0 overrides-7.7.0 posthog-4.3.2 protobuf-5.29.5 pyasn1-0.6.1 pyasn1-modules-0.4.2 pypika-0.48.9 pyproject_hooks-1.2.0 referencing-0.36.2 requests-oauthlib-2.0.0 rich-14.0.0 rpds-py-0.25.1 rsa-4.9.1 shellingham-1.5.4 starlette-0.45.3 sympy-1.14.0 tokenizers-0.21.1 tomli-2.2.1 typer-0.16.0 uvicorn-0.34.3 uvloop-0.21.0 watchfiles-1.0.5 websocket-client-1.8.0 websockets-15.0.1 zipp-3.22.0\n", "✅ chromadb installed and working\n", "📦 Installing sentence-transformers...\n", "Collecting sentence-transformers\n", "  Downloading sentence_transformers-4.1.0-py3-none-any.whl.metadata (13 kB)\n", "Collecting transformers<5.0.0,>=4.41.0 (from sentence-transformers)\n", "  Downloading transformers-4.52.4-py3-none-any.whl.metadata (38 kB)\n", "Requirement already satisfied: tqdm in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from sentence-transformers) (4.67.1)\n", "Collecting torch>=1.11.0 (from sentence-transformers)\n", "  Downloading torch-2.7.1-cp310-none-macosx_11_0_arm64.whl.metadata (29 kB)\n", "Collecting scikit-learn (from sentence-transformers)\n", "  Downloading scikit_learn-1.7.0-cp310-cp310-macosx_12_0_arm64.whl.metadata (31 kB)\n", "Collecting scipy (from sentence-transformers)\n", "  Downloading scipy-1.15.3-cp310-cp310-macosx_14_0_arm64.whl.metadata (61 kB)\n", "Requirement already satisfied: huggingface-hub>=0.20.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from sentence-transformers) (0.32.4)\n", "Collecting <PERSON><PERSON> (from sentence-transformers)\n", "  Downloading pillow-11.2.1-cp310-cp310-macosx_11_0_arm64.whl.metadata (8.9 kB)\n", "Requirement already satisfied: typing_extensions>=4.5.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from sentence-transformers) (4.14.0)\n", "Requirement already satisfied: filelock in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (3.18.0)\n", "Requirement already satisfied: numpy>=1.17 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2.2.6)\n", "Requirement already satisfied: packaging>=20.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2024.11.6)\n", "Requirement already satisfied: requests in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.21.1)\n", "Collecting safetensors>=0.4.3 (from transformers<5.0.0,>=4.41.0->sentence-transformers)\n", "  Downloading safetensors-0.5.3-cp38-abi3-macosx_11_0_arm64.whl.metadata (3.8 kB)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2025.5.1)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (1.1.3)\n", "Requirement already satisfied: sympy>=1.13.3 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from torch>=1.11.0->sentence-transformers) (1.14.0)\n", "Collecting networkx (from torch>=1.11.0->sentence-transformers)\n", "  Using cached networkx-3.4.2-py3-none-any.whl.metadata (6.3 kB)\n", "Collecting jinja2 (from torch>=1.11.0->sentence-transformers)\n", "  Using cached jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from sympy>=1.13.3->torch>=1.11.0->sentence-transformers) (1.3.0)\n", "Collecting MarkupSafe>=2.0 (from jinja2->torch>=1.11.0->sentence-transformers)\n", "  Using cached MarkupSafe-3.0.2-cp310-cp310-macosx_11_0_arm64.whl.metadata (4.0 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (2025.4.26)\n", "Requirement already satisfied: joblib>=1.2.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from scikit-learn->sentence-transformers) (1.5.1)\n", "Collecting threadpoolctl>=3.1.0 (from scikit-learn->sentence-transformers)\n", "  Using cached threadpoolctl-3.6.0-py3-none-any.whl.metadata (13 kB)\n", "Downloading sentence_transformers-4.1.0-py3-none-any.whl (345 kB)\n", "Downloading transformers-4.52.4-py3-none-any.whl (10.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.5/10.5 MB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m0:01\u001b[0m\n", "\u001b[?25hDownloading safetensors-0.5.3-cp38-abi3-macosx_11_0_arm64.whl (418 kB)\n", "Downloading torch-2.7.1-cp310-none-macosx_11_0_arm64.whl (68.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m68.6/68.6 MB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hUsing cached jinja2-3.1.6-py3-none-any.whl (134 kB)\n", "Using cached MarkupSafe-3.0.2-cp310-cp310-macosx_11_0_arm64.whl (12 kB)\n", "Using cached networkx-3.4.2-py3-none-any.whl (1.7 MB)\n", "Downloading pillow-11.2.1-cp310-cp310-macosx_11_0_arm64.whl (3.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.0/3.0 MB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading scikit_learn-1.7.0-cp310-cp310-macosx_12_0_arm64.whl (10.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.7/10.7 MB\u001b[0m \u001b[31m7.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading scipy-1.15.3-cp310-cp310-macosx_14_0_arm64.whl (22.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m22.4/22.4 MB\u001b[0m \u001b[31m9.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25hUsing cached threadpoolctl-3.6.0-py3-none-any.whl (18 kB)\n", "Installing collected packages: threadpoolctl, scipy, safetensors, Pillow, networkx, MarkupSafe, scikit-learn, jinja2, torch, transformers, sentence-transformers\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11/11\u001b[0m [sentence-transformers]ence-transformers]\n", "\u001b[1A\u001b[2KSuccessfully installed MarkupSafe-3.0.2 Pillow-11.2.1 jinja2-3.1.6 networkx-3.4.2 safetensors-0.5.3 scikit-learn-1.7.0 scipy-1.15.3 sentence-transformers-4.1.0 threadpoolctl-3.6.0 torch-2.7.1 transformers-4.52.4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ sentence-transformers installed and working\n", "📦 Installing faiss-cpu...\n", "Collecting faiss-cpu\n", "  Downloading faiss_cpu-1.11.0-cp310-cp310-macosx_14_0_arm64.whl.metadata (4.8 kB)\n", "Requirement already satisfied: numpy<3.0,>=1.25.0 in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from faiss-cpu) (2.2.6)\n", "Requirement already satisfied: packaging in /opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages (from faiss-cpu) (24.2)\n", "Downloading faiss_cpu-1.11.0-cp310-cp310-macosx_14_0_arm64.whl (3.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.3/3.3 MB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: faiss-cpu\n", "Successfully installed faiss-cpu-1.11.0\n", "✅ faiss-cpu installed and working\n", "\n", "✅ Vector search packages installation complete!\n"]}], "source": ["# Install vector search packages\n", "print(\"🔍 Installing Vector Search Packages...\")\n", "print(\"=\" * 50)\n", "\n", "vector_packages = [\n", "    (\"chromadb\", \"chromadb\"),\n", "    (\"sentence-transformers\", \"sentence_transformers\"),\n", "    (\"faiss-cpu\", \"faiss\")\n", "]\n", "\n", "for package, import_name in vector_packages:\n", "    install_and_test_package(package, import_name)\n", "\n", "print(\"\\n✅ Vector search packages installation complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install UI packages\n", "print(\"🖥️ Installing User Interface Packages...\")\n", "print(\"=\" * 50)\n", "\n", "ui_packages = [\n", "    (\"streamlit\", \"streamlit\"),\n", "    (\"gradio\", \"gradio\")\n", "]\n", "\n", "for package, import_name in ui_packages:\n", "    install_and_test_package(package, import_name)\n", "\n", "print(\"\\n✅ User interface packages installation complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1.3: Environment Variables Setup\n", "\n", "**What we're doing**: Setting up secure storage for API keys\n", "\n", "**Why this matters**:\n", "- **Security**: API keys are like passwords - keep them secret\n", "- **Flexibility**: Easy to change keys without changing code\n", "- **Best Practice**: Industry standard for configuration\n", "\n", "**How it works**: We store secrets in a `.env` file that our code reads"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔐 Environment Variables Setup\n", "========================================\n", "✅ .env file found\n", "\n", "🔍 Checking API keys:\n", "  ✅ OPENAI_API_KEY: sk-proj-C9...\n", "  ✅ GROQ_API_KEY: gsk_H50OyL...\n", "  ✅ LANGCHAIN_API_KEY: lsv2_pt_eb...\n", "  ✅ LANGCHAIN_PROJECT: AI-Applica...\n"]}], "source": ["# Check if .env file exists\n", "import os\n", "from pathlib import Path\n", "\n", "env_file = Path(\".env\")\n", "\n", "print(\"🔐 Environment Variables Setup\")\n", "print(\"=\" * 40)\n", "\n", "if env_file.exists():\n", "    print(\"✅ .env file found\")\n", "    \n", "    # Load and check environment variables\n", "    from dotenv import load_dotenv\n", "    load_dotenv()\n", "    \n", "    # Check for required API keys\n", "    required_keys = [\n", "        \"OPENAI_API_KEY\",\n", "        \"GROQ_API_KEY\",\n", "        \"LANGCHAIN_API_KEY\",\n", "        \"LANGCHAIN_PROJECT\"\n", "    ]\n", "    \n", "    print(\"\\n🔍 Checking API keys:\")\n", "    for key in required_keys:\n", "        value = os.getenv(key)\n", "        if value:\n", "            # Show only first 10 characters for security\n", "            masked_value = value[:10] + \"...\" if len(value) > 10 else value\n", "            print(f\"  ✅ {key}: {masked_value}\")\n", "        else:\n", "            print(f\"  ❌ {key}: Not found\")\n", "            \n", "else:\n", "    print(\"❌ .env file not found\")\n", "    print(\"\\n📝 Creating template .env file...\")\n", "    \n", "    template_content = '''# API Keys for AI Application\n", "# Replace the placeholder values with your actual API keys\n", "\n", "# OpenAI API Key (get from https://platform.openai.com/api-keys)\n", "OPENAI_API_KEY=\"your-openai-api-key-here\"\n", "\n", "# Groq API Key (get from https://console.groq.com/keys)\n", "GROQ_API_KEY=\"your-groq-api-key-here\"\n", "\n", "# LangChain API Key (get from https://smith.langchain.com/)\n", "LANGCHAIN_API_KEY=\"your-langchain-api-key-here\"\n", "LANGCHAIN_PROJECT=\"AI-Application\"\n", "LANGCHAIN_TRACING_V2=\"true\"\n", "\n", "# Optional: Other API keys\n", "HUGGINGFACE_API_KEY=\"your-huggingface-key-here\"\n", "'''\n", "    \n", "    with open(\".env\", \"w\") as f:\n", "        f.write(template_content)\n", "    \n", "    print(\"✅ Template .env file created\")\n", "    print(\"\\n⚠️ IMPORTANT: Please edit the .env file and add your actual API keys\")\n", "    print(\"\\n🔗 Get API keys from:\")\n", "    print(\"  • OpenAI: https://platform.openai.com/api-keys\")\n", "    print(\"  • Groq: https://console.groq.com/keys\")\n", "    print(\"  • LangChain: https://smith.langchain.com/\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1.4: Test All Connections\n", "\n", "**What we're doing**: Verifying that all our AI services work\n", "\n", "**Why this is crucial**: Better to find problems now than during development\n", "\n", "**What we're testing**:\n", "1. OpenAI connection and response\n", "2. Groq connection and response  \n", "3. <PERSON><PERSON><PERSON><PERSON> tracing\n", "4. Basic chain functionality"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 Testing OpenAI Connection...\n", "========================================\n", "✅ OpenAI model created: gpt-3.5-turbo\n", "📝 Test response: Hey there, sending warm greetings from OpenAI!\n", "✅ OpenAI connection successful!\n"]}], "source": ["# Test OpenAI connection\n", "print(\"🤖 Testing OpenAI Connection...\")\n", "print(\"=\" * 40)\n", "\n", "try:\n", "    from langchain_openai import ChatOpenAI\n", "    from dotenv import load_dotenv\n", "    \n", "    # Load environment variables\n", "    load_dotenv()\n", "    \n", "    # Set environment variables for this session\n", "    os.environ[\"OPENAI_API_KEY\"] = os.getenv(\"OPENAI_API_KEY\")\n", "    \n", "    # Create OpenAI model\n", "    openai_model = ChatOpenAI(\n", "        model=\"gpt-3.5-turbo\",\n", "        temperature=0.7,\n", "        max_tokens=50  # Short response for testing\n", "    )\n", "    \n", "    print(f\"✅ OpenAI model created: {openai_model.model_name}\")\n", "    \n", "    # Test with a simple question\n", "    test_response = openai_model.invoke(\"Say 'Hello from OpenAI!' in a friendly way.\")\n", "    print(f\"📝 Test response: {test_response.content}\")\n", "    print(\"✅ OpenAI connection successful!\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ OpenAI connection failed: {e}\")\n", "    print(\"💡 Check your OPENAI_API_KEY in the .env file\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "⚡ Testing Groq Connection...\n", "========================================\n", "✅ Groq model created: gemma2-9b-it\n", "📝 Test response: Hey there! 👋  Just sayin' hello from G<PERSON>q! 😊  \n", "\n", "✅ Groq connection successful!\n"]}], "source": ["# Test Groq connection\n", "print(\"\\n⚡ Testing Groq Connection...\")\n", "print(\"=\" * 40)\n", "\n", "try:\n", "    from langchain_groq import ChatGroq\n", "    \n", "    # Set Groq API key\n", "    os.environ[\"GROQ_API_KEY\"] = os.getenv(\"GROQ_API_KEY\")\n", "    \n", "    # Create Groq model\n", "    groq_model = ChatGroq(\n", "        model=\"gemma2-9b-it\",\n", "        temperature=0.7,\n", "        max_tokens=50  # Short response for testing\n", "    )\n", "    \n", "    print(f\"✅ Groq model created: {groq_model.model_name}\")\n", "    \n", "    # Test with a simple question\n", "    test_response = groq_model.invoke(\"Say 'Hello from <PERSON><PERSON>q!' in a friendly way.\")\n", "    print(f\"📝 Test response: {test_response.content}\")\n", "    print(\"✅ Groq connection successful!\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Groq connection failed: {e}\")\n", "    print(\"💡 Check your GROQ_API_KEY in the .env file\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Testing LangChain Tracing...\n", "========================================\n", "✅ LangChain project: AI-Application\n", "✅ Tracing enabled: true\n", "📊 Your AI interactions will be tracked in LangSmith\n"]}], "source": ["# Test <PERSON><PERSON> tracing\n", "print(\"\\n📊 Testing LangChain Tracing...\")\n", "print(\"=\" * 40)\n", "\n", "try:\n", "    # Set LangChain environment variables\n", "    os.environ[\"LANGCHAIN_API_KEY\"] = os.getenv(\"LANGCHAIN_API_KEY\")\n", "    os.environ[\"LANGCHAIN_PROJECT\"] = os.getenv(\"LANGCHAIN_PROJECT\")\n", "    os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "    \n", "    print(f\"✅ LangChain project: {os.getenv('LANGCHAIN_PROJECT')}\")\n", "    print(f\"✅ Tracing enabled: {os.getenv('LANGCHAIN_TRACING_V2')}\")\n", "    print(\"📊 Your AI interactions will be tracked in LangSmith\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ LangChain tracing setup failed: {e}\")\n", "    print(\"💡 Check your LANGCHAIN_API_KEY in the .env file\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔗 Testing Basic Chain Functionality...\n", "========================================\n", "✅ Chain created successfully\n", "📝 Chain test result: 4\n", "✅ Chain functionality working!\n"]}], "source": ["# Test basic chain functionality\n", "print(\"\\n🔗 Testing Basic Chain Functionality...\")\n", "print(\"=\" * 40)\n", "\n", "try:\n", "    from langchain_core.prompts import ChatPromptTemplate\n", "    from langchain_core.output_parsers import StrOutputParser\n", "    \n", "    # Create a simple chain\n", "    prompt = ChatPromptTemplate.from_messages([\n", "        (\"system\", \"You are a helpful assistant. Be brief and friendly.\"),\n", "        (\"user\", \"{question}\")\n", "    ])\n", "    \n", "    parser = StrOutputParser()\n", "    \n", "    # Create chain with Groq (faster for testing)\n", "    chain = prompt | groq_model | parser\n", "    \n", "    print(\"✅ Chain created successfully\")\n", "    \n", "    # Test the chain\n", "    result = chain.invoke({\"question\": \"What is 2+2? Just give the number.\"})\n", "    print(f\"📝 Chain test result: {result}\")\n", "    print(\"✅ Chain functionality working!\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ Chain test failed: {e}\")\n", "    print(\"💡 Check that all previous tests passed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1.5: Create Requirements File\n", "\n", "**What we're doing**: Creating a list of all packages for easy installation\n", "\n", "**Why this matters**: Others can recreate your exact environment\n", "\n", "**Professional practice**: Always include requirements.txt in projects"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 Creating requirements.txt file...\n", "✅ requirements.txt created\n", "\n", "📝 Contents:\n", "# Core LangChain packages\n", "langchain>=0.1.0\n", "langchain-openai>=0.1.0\n", "langchain-groq>=0.1.0\n", "langchain-core>=0.1.0\n", "langchain-community>=0.1.0\n", "\n", "# Utility packages\n", "python-dotenv>=1.0.0\n", "pydantic>=2.0.0\n", "requests>=2.31.0\n", "pandas>=2.0.0\n", "numpy>=1.24.0\n", "\n", "# Document processing\n", "pypdf>=3.0.0\n", "python-docx>=0.8.11\n", "beautifulsoup4>=4.12.0\n", "unstructured>=0.10.0\n", "\n", "# Vector search and embeddings\n", "chromadb>=0.4.0\n", "sentence-transformers>=2.2.0\n", "faiss-cpu>=1.7.0\n", "\n", "# User interface\n", "streamlit>=1.28.0\n", "gradio>=3.50.0\n", "\n", "# Development tools\n", "jupyter>=1.0.0\n", "ipykernel>=6.25.0\n", "\n", "\n", "💡 Others can now install all dependencies with:\n", "   pip install -r requirements.txt\n"]}], "source": ["# Create requirements.txt file\n", "print(\"📋 Creating requirements.txt file...\")\n", "\n", "requirements_content = '''# Core LangChain packages\n", "langchain>=0.1.0\n", "langchain-openai>=0.1.0\n", "langchain-groq>=0.1.0\n", "langchain-core>=0.1.0\n", "langchain-community>=0.1.0\n", "\n", "# Utility packages\n", "python-dotenv>=1.0.0\n", "pydantic>=2.0.0\n", "requests>=2.31.0\n", "pandas>=2.0.0\n", "numpy>=1.24.0\n", "\n", "# Document processing\n", "pypdf>=3.0.0\n", "python-docx>=0.8.11\n", "beautifulsoup4>=4.12.0\n", "unstructured>=0.10.0\n", "\n", "# Vector search and embeddings\n", "chromadb>=0.4.0\n", "sentence-transformers>=2.2.0\n", "faiss-cpu>=1.7.0\n", "\n", "# User interface\n", "streamlit>=1.28.0\n", "gradio>=3.50.0\n", "\n", "# Development tools\n", "jupyter>=1.0.0\n", "ipykernel>=6.25.0\n", "'''\n", "\n", "with open(\"requirements.txt\", \"w\") as f:\n", "    f.write(requirements_content)\n", "\n", "print(\"✅ requirements.txt created\")\n", "print(\"\\n📝 Contents:\")\n", "print(requirements_content)\n", "\n", "print(\"\\n💡 Others can now install all dependencies with:\")\n", "print(\"   pip install -r requirements.txt\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Step 1 Complete!\n", "\n", "### ✅ What We Accomplished\n", "1. **Environment Verification**: Confirmed we have a proper Python environment\n", "2. **Package Installation**: Installed all required packages for our AI application\n", "3. **API Key Setup**: Configured secure storage for API keys\n", "4. **Connection Testing**: Verified all AI services work correctly\n", "5. **Documentation**: Created requirements.txt for reproducibility\n", "\n", "### 🔧 Technical Skills Gained\n", "- **Environment Management**: Professional Python environment setup\n", "- **Package Management**: Installing and testing dependencies\n", "- **Security Practices**: Safe API key handling\n", "- **Testing**: Verifying system functionality\n", "- **Documentation**: Creating reproducible environments\n", "\n", "### 🚀 Ready for Next Step\n", "Your development environment is now ready for building a complete AI application!\n", "\n", "**Next**: We'll move to **Step 2: AI Model Integration Review** where we'll:\n", "- Review different AI models and their strengths\n", "- Build a model manager for switching between models\n", "- Implement model comparison functionality\n", "- Add performance monitoring\n", "\n", "### 💡 Key Takeaways\n", "1. **Preparation is crucial**: A good environment setup prevents many problems\n", "2. **Test early and often**: Verify each component works before building on it\n", "3. **Security matters**: Always protect API keys and sensitive information\n", "4. **Documentation helps**: Requirements files make projects reproducible\n", "5. **Professional practices**: Follow industry standards from the beginning\n", "\n", "Great job! You now have a solid foundation for AI application development! 🎉"]}], "metadata": {"kernelspec": {"display_name": "agentic-2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 2}