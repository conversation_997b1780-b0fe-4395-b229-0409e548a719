{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📚 Assignment 03: Prompt Templates\n", "\n", "## 🎯 What We're Going to Do\n", "In this notebook, we'll learn how to create reusable prompt templates that make our AI interactions consistent and professional.\n", "\n", "## 🧠 Learning Goals\n", "- Create and use prompt templates\n", "- Understand different message types\n", "- Build consistent AI personalities\n", "- Make reusable AI components"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup Environment\n", "\n", "**What we're doing**: Setting up our workspace and importing the tools we need\n", "\n", "**Why this matters**: We need prompt template tools from LangChain"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Environment setup complete!\n", "🤖 AI model ready for prompt template experiments!\n"]}], "source": ["# Import required packages\n", "import os\n", "from dotenv import load_dotenv\n", "from langchain_core.prompts import ChatPromptTemplate, PromptTemplate\n", "from langchain_groq import ChatGroq\n", "from langchain_openai import ChatOpenAI\n", "\n", "# Load environment variables\n", "load_dotenv()\n", "os.environ[\"GROQ_API_KEY\"] = os.getenv(\"GROQ_API_KEY\")\n", "os.environ[\"OPENAI_API_KEY\"] = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "# Create our AI model (using Groq for speed)\n", "model = ChatGroq(model=\"gemma2-9b-it\", temperature=0.7)\n", "\n", "print(\"✅ Environment setup complete!\")\n", "print(\"🤖 AI model ready for prompt template experiments!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Your First Prompt Template\n", "\n", "**What we're doing**: Creating a simple template with variables\n", "\n", "**Why this matters**: Templates let us reuse the same structure with different content\n", "\n", "**Key concept**: Variables in curly braces {} get replaced with actual values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📝 Template created!\n", "Template: Explain {topic} in simple terms for a {audience}.\n", "Variables: ['audience', 'topic']\n", "\n", "🎯 Filled prompt: Explain machine learning in simple terms for a 10-year-old child.\n"]}], "source": ["# Create a simple prompt template\n", "simple_template = PromptTemplate(\n", "    template=\"Explain {what is AI} in simple terms for a {devoos engg}.\",\n", "    input_variables=[\"topic\", \"audience\"]\n", ")\n", "\n", "print(\"📝 Template created!\")\n", "print(f\"Template: {simple_template.template}\")\n", "print(f\"Variables: {simple_template.input_variables}\")\n", "\n", "# Test the template\n", "filled_prompt = simple_template.format(\n", "    topic=\"machine learning\",\n", "    audience=\"10-year-old child\"\n", ")\n", "\n", "print(f\"\\n🎯 Filled prompt: {filled_prompt}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Chat Prompt Templates\n", "\n", "**What we're doing**: Creating templates with different message types\n", "\n", "**Why this matters**: Chat models work better with structured conversations\n", "\n", "**Message types**:\n", "- **system**: Instructions for the AI's behavior\n", "- **user**: The actual question or request\n", "- **assistant**: Previous AI responses (for context)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a chat prompt template\n", "chat_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a helpful {role}. Always be {personality} and provide {detail_level} explanations.\"),\n", "    (\"user\", \"{question}\")\n", "])\n", "\n", "print(\"💬 Chat template created!\")\n", "print(\"Template structure:\")\n", "for i, message in enumerate(chat_template.messages):\n", "    print(f\"  {i+1}. {message.prompt.template} (type: {type(message).__name__})\")\n", "\n", "# Test the chat template\n", "filled_messages = chat_template.format_messages(\n", "    role=\"science teacher\",\n", "    personality=\"enthusiastic\",\n", "    detail_level=\"detailed\",\n", "    question=\"How do plants make food?\"\n", ")\n", "\n", "print(\"\\n🎯 Filled messages:\")\n", "for msg in filled_messages:\n", "    print(f\"  {msg.type}: {msg.content}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Testing Templates with AI\n", "\n", "**What we're doing**: Using our templates to get AI responses\n", "\n", "**Why this matters**: This shows how templates work in real applications\n", "\n", "**What to observe**: How the AI follows the template instructions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the chat template with our AI model\n", "response = model.invoke(filled_messages)\n", "\n", "print(\"🤖 AI Response using template:\")\n", "print(\"=\" * 50)\n", "print(response.content)\n", "print(\"=\" * 50)\n", "\n", "# Now let's try the same question without a template\n", "simple_question = \"How do plants make food?\"\n", "simple_response = model.invoke(simple_question)\n", "\n", "print(\"\\n🤖 AI Response without template:\")\n", "print(\"=\" * 50)\n", "print(simple_response.content)\n", "print(\"=\" * 50)\n", "\n", "print(\"\\n💡 Notice how the template made the response more structured and enthusiastic!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Creating Specialized Templates\n", "\n", "**What we're doing**: Building templates for specific use cases\n", "\n", "**Why this matters**: Different tasks need different approaches\n", "\n", "**Examples**: Customer service, tutoring, creative writing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Template 1: Customer Service Bot\n", "customer_service_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a friendly customer service representative for {company}. \"\n", "               \"Always be polite, helpful, and professional. \"\n", "               \"If you can't help, offer to escalate to a human agent.\"),\n", "    (\"user\", \"{customer_issue}\")\n", "])\n", "\n", "# Template 2: <PERSON>\n", "math_tutor_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a patient math tutor for {grade_level} students. \"\n", "               \"Break down problems step-by-step. \"\n", "               \"Use encouraging language and provide examples.\"),\n", "    (\"user\", \"Help me with this math problem: {problem}\")\n", "])\n", "\n", "# Template 3: Creative Writing Assistant\n", "creative_writing_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"You are a creative writing coach specializing in {genre}. \"\n", "               \"Help writers improve their {writing_aspect}. \"\n", "               \"Be encouraging and provide specific, actionable feedback.\"),\n", "    (\"user\", \"{writing_request}\")\n", "])\n", "\n", "print(\"✅ Created 3 specialized templates:\")\n", "print(\"1. 🏢 Customer Service Bot\")\n", "print(\"2. 📚 Math Tutor\")\n", "print(\"3. ✍️ Creative Writing Assistant\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Testing Specialized Templates\n", "\n", "**What we're doing**: Trying out our specialized templates\n", "\n", "**Why this matters**: See how templates create different AI personalities\n", "\n", "**What to observe**: How the same AI model behaves differently with different templates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Customer Service Template\n", "print(\"🏢 Testing Customer Service Template:\")\n", "cs_messages = customer_service_template.format_messages(\n", "    company=\"TechGadgets Inc.\",\n", "    customer_issue=\"My laptop won't turn on after the latest update.\"\n", ")\n", "cs_response = model.invoke(cs_messages)\n", "print(f\"Response: {cs_response.content}\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "\n", "# Test Math Tutor Template\n", "print(\"\\n📚 Testing Math Tutor Template:\")\n", "math_messages = math_tutor_template.format_messages(\n", "    grade_level=\"5th grade\",\n", "    problem=\"What is 15 × 24?\"\n", ")\n", "math_response = model.invoke(math_messages)\n", "print(f\"Response: {math_response.content}\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "\n", "# Test Creative Writing Template\n", "print(\"\\n✍️ Testing Creative Writing Template:\")\n", "writing_messages = creative_writing_template.format_messages(\n", "    genre=\"science fiction\",\n", "    writing_aspect=\"character development\",\n", "    writing_request=\"How can I make my robot character more relatable?\"\n", ")\n", "writing_response = model.invoke(writing_messages)\n", "print(f\"Response: {writing_response.content}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Template with Multiple Variables\n", "\n", "**What we're doing**: Creating a complex template with many customization options\n", "\n", "**Why this matters**: Real applications often need many parameters\n", "\n", "**What to observe**: How multiple variables give fine control over AI behavior"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a comprehensive template\n", "comprehensive_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", \n", "     \"You are a {role} with {experience_level} experience. \"\n", "     \"Your audience is {audience} with {knowledge_level} knowledge of {subject}. \"\n", "     \"Your communication style should be {tone} and {formality_level}. \"\n", "     \"Provide {response_length} responses with {detail_level} detail.\"),\n", "    (\"user\", \"{user_request}\")\n", "])\n", "\n", "print(\"🎛️ Comprehensive template created with 8 variables!\")\n", "print(\"Variables:\", comprehensive_template.input_variables)\n", "\n", "# Test with different configurations\n", "configs = [\n", "    {\n", "        \"role\": \"software engineer\",\n", "        \"experience_level\": \"senior\",\n", "        \"audience\": \"beginners\",\n", "        \"knowledge_level\": \"basic\",\n", "        \"subject\": \"programming\",\n", "        \"tone\": \"encouraging\",\n", "        \"formality_level\": \"casual\",\n", "        \"response_length\": \"concise\",\n", "        \"detail_level\": \"high\",\n", "        \"user_request\": \"Explain what a function is in programming\"\n", "    },\n", "    {\n", "        \"role\": \"university professor\",\n", "        \"experience_level\": \"expert\",\n", "        \"audience\": \"graduate students\",\n", "        \"knowledge_level\": \"advanced\",\n", "        \"subject\": \"machine learning\",\n", "        \"tone\": \"analytical\",\n", "        \"formality_level\": \"formal\",\n", "        \"response_length\": \"detailed\",\n", "        \"detail_level\": \"technical\",\n", "        \"user_request\": \"Explain gradient descent optimization\"\n", "    }\n", "]\n", "\n", "for i, config in enumerate(configs, 1):\n", "    print(f\"\\n🧪 Test Configuration {i}:\")\n", "    messages = comprehensive_template.format_messages(**config)\n", "    response = model.invoke(messages)\n", "    print(f\"Request: {config['user_request']}\")\n", "    print(f\"Response: {response.content[:200]}...\")  # First 200 characters\n", "    print(\"-\" * 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Template Validation and E<PERSON><PERSON> Handling\n", "\n", "**What we're doing**: Making our templates robust and error-proof\n", "\n", "**Why this matters**: Real applications need to handle missing or invalid inputs\n", "\n", "**What to observe**: How to build templates that fail gracefully"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to safely use templates\n", "def safe_template_invoke(template, model, **kwargs):\n", "    \"\"\"\n", "    Safely invoke a template with error handling\n", "    \"\"\"\n", "    try:\n", "        # Check if all required variables are provided\n", "        missing_vars = set(template.input_variables) - set(kwargs.keys())\n", "        if missing_vars:\n", "            return f\"❌ Missing required variables: {missing_vars}\"\n", "        \n", "        # Format and invoke\n", "        messages = template.format_messages(**kwargs)\n", "        response = model.invoke(messages)\n", "        return f\"✅ Success: {response.content}\"\n", "        \n", "    except Exception as e:\n", "        return f\"❌ Error: {str(e)}\"\n", "\n", "# Test with missing variables\n", "print(\"🧪 Testing error handling:\")\n", "\n", "# Test 1: Missing variables\n", "result1 = safe_template_invoke(\n", "    customer_service_template, \n", "    model,\n", "    company=\"TechCorp\"  # Missing customer_issue\n", ")\n", "print(f\"Test 1 (missing variable): {result1}\")\n", "\n", "# Test 2: All variables provided\n", "result2 = safe_template_invoke(\n", "    customer_service_template,\n", "    model,\n", "    company=\"TechCorp\",\n", "    customer_issue=\"I need help with my account\"\n", ")\n", "print(f\"\\nTest 2 (all variables): {result2[:100]}...\")  # First 100 characters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Assignment Tasks - Your Turn!\n", "\n", "Now it's your turn to create and test prompt templates!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1: Create Your Own Templates\n", "Create templates for these scenarios:\n", "1. **Fitness Coach**: Helps with workout plans\n", "2. **Travel Guide**: Provides travel recommendations\n", "3. **Recipe Assistant**: Helps with cooking\n", "4. **Study Buddy**: Helps with exam preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Task 1: Create your templates here\n", "\n", "# 1. Fitness Coach Template\n", "fitness_template = ChatPromptTemplate.from_messages([\n", "    # Your template here...\n", "])\n", "\n", "# 2. Travel Guide Template\n", "travel_template = ChatPromptTemplate.from_messages([\n", "    # Your template here...\n", "])\n", "\n", "# 3. <PERSON><PERSON><PERSON> Assistant Template\n", "recipe_template = ChatPromptTemplate.from_messages([\n", "    # Your template here...\n", "])\n", "\n", "# 4. <PERSON>late\n", "study_template = ChatPromptTemplate.from_messages([\n", "    # Your template here...\n", "])\n", "\n", "print(\"✅ Created 4 custom templates!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 2: Test Your Templates\n", "Test each of your templates with realistic scenarios"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Task 2: Test your templates here\n", "\n", "# Test scenarios:\n", "test_cases = {\n", "    \"fitness\": {\n", "        # Your test variables here...\n", "    },\n", "    \"travel\": {\n", "        # Your test variables here...\n", "    },\n", "    \"recipe\": {\n", "        # Your test variables here...\n", "    },\n", "    \"study\": {\n", "        # Your test variables here...\n", "    }\n", "}\n", "\n", "# Test each template\n", "# Your testing code here..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3: Compare Template vs No Template\n", "Ask the same question with and without a template to see the difference"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Task 3: Comparison test\n", "\n", "question = \"How do I lose weight?\"\n", "\n", "# Without template\n", "no_template_response = model.invoke(question)\n", "print(\"🤖 Without template:\")\n", "print(no_template_response.content)\n", "\n", "print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "\n", "# With your fitness template\n", "# Your code here...\n", "\n", "print(\"\\n💡 What differences do you notice?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 4: Template Optimization\n", "Take one of your templates and create 3 different versions with different approaches. Test which works best."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Task 4: Create 3 versions of the same template\n", "\n", "# Version 1: Simple and direct\n", "template_v1 = ChatPromptTemplate.from_messages([\n", "    # Your simple version here...\n", "])\n", "\n", "# Version 2: Detailed and structured\n", "template_v2 = ChatPromptTemplate.from_messages([\n", "    # Your detailed version here...\n", "])\n", "\n", "# Version 3: Creative and engaging\n", "template_v3 = ChatPromptTemplate.from_messages([\n", "    # Your creative version here...\n", "])\n", "\n", "# Test all three with the same input\n", "# Your testing code here...\n", "\n", "print(\"Which version worked best and why?\")\n", "# Your analysis here..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎓 What You Learned\n", "\n", "Congratulations! You now understand:\n", "\n", "✅ **Prompt Templates**: How to create reusable AI interaction patterns  \n", "✅ **Message Types**: System, user, and assistant message roles  \n", "✅ **Variable Substitution**: How to make templates flexible with variables  \n", "✅ **Specialized Templates**: Creating templates for specific use cases  \n", "✅ **Error Handling**: Making templates robust and reliable  \n", "✅ **Template Optimization**: Testing and improving template effectiveness  \n", "\n", "## 🚀 Next Steps\n", "\n", "Ready for **Assignment 04: Chains and Pipelines**? You'll learn:\n", "- How to connect templates to AI models\n", "- How to build AI processing pipelines\n", "- How to create complex AI workflows\n", "\n", "## 💡 Key Takeaways\n", "\n", "1. **Templates = Consistency**: Same structure, predictable results\n", "2. **Variables = Flexibility**: One template, many use cases\n", "3. **System Messages = Personality**: Define how AI should behave\n", "4. **Testing = Quality**: Always test templates with real scenarios\n", "5. **Error <PERSON> = Reliability**: Plan for when things go wrong\n", "\n", "## 🏆 Professional Tip\n", "\n", "In real applications, you'll often have a library of templates for different purposes. Start building your template collection now!\n", "\n", "Great job! 🎉"]}], "metadata": {"kernelspec": {"display_name": "agentic-2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 2}