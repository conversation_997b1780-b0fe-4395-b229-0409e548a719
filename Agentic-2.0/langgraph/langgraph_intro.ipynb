{"cells": [{"cell_type": "markdown", "id": "tutorial-header", "metadata": {}, "source": ["# **Complete LangGraph Tutorial: Building AI Workflows Step by Step**\n", "\n", "## **What Are We Learning?**\n", "\n", "**LangGraph** is a powerful framework for building **workflows** (step-by-step processes) for AI applications. Think of it like creating a factory assembly line where:\n", "- Each **station** (node) does a specific job\n", "- **Conveyor belts** (edges) move data between stations\n", "- The **factory manager** (graph) coordinates everything\n", "\n", "## **What Problem Are We Solving?**\n", "\n", "Instead of manually calling functions one by one, LangGraph helps us:\n", "1. **Automate workflows** - Let the system handle the sequence\n", "2. **Chain AI operations** - Connect multiple AI tasks together\n", "3. **Build complex applications** - Create sophisticated AI systems\n", "4. **Visualize processes** - See how data flows through our system\n", "\n", "## **Why Use LangGraph?**\n", "\n", "- **Organization**: Clean, structured code\n", "- **Reusability**: Build once, use many times\n", "- **Scalability**: Easy to add more steps\n", "- **Visualization**: See your workflow as a diagram\n", "- **Error <PERSON>**: Built-in error management\n", "\n", "---\n", "\n", "## **Step 1: Environment Check and Imports**\n", "\n", "Let's make sure everything is working properly and import what we need:"]}, {"cell_type": "code", "execution_count": 1, "id": "bbc3cb43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ LangGraph imported successfully!\n", "✅ Environment setup complete!\n", "✅ Ready to learn LangGraph!\n"]}], "source": ["from langgraph.graph import Graph\n", "from IPython.display import Image, display\n", "\n", "print(\"✅ LangGraph imported successfully!\")\n", "print(\"✅ Environment setup complete!\")\n", "print(\"✅ Ready to learn LangGraph!\")"]}, {"cell_type": "markdown", "id": "concepts-overview", "metadata": {}, "source": ["## **Step 2: Understanding Core Concepts**\n", "\n", "Before we start coding, let's understand the key concepts:\n", "\n", "### **🔧 Key Components:**\n", "- **`Graph()`** - The workflow container (like a factory)\n", "- **`Nodes`** - Processing stations (functions that do work)\n", "- **`Edges`** - Connections between nodes (data flow paths)\n", "- **`Entry Point`** - Where the workflow starts\n", "- **`Finish Point`** - Where the workflow ends\n", "- **`invoke()`** - Run the workflow with input data\n", "- **`StateGraph()`** - Advanced graph with shared state (we'll learn this later)\n", "\n", "### **📊 Flow Diagram:**\n", "```\n", "Input → [Node 1] → [Node 2] → [Node 3] → Output\n", "         ↓           ↓           ↓\n", "    Process A   Process B   Process C\n", "```"]}, {"cell_type": "markdown", "id": "basic-example-intro", "metadata": {}, "source": ["## **Step 3: Creating Our First Simple Functions**\n", "\n", "Let's start with simple functions that we'll connect together. Think of these as **workers** in our factory:\n", "\n", "### **🔍 What We're Building:**\n", "- **Function 1**: Takes text and adds \" from first function\"\n", "- **Function 2**: Takes text and adds \" savita from second function\"\n", "- **Function 3**: A placeholder for future use\n", "\n", "### **💡 Why This Approach:**\n", "- Start simple to understand the concepts\n", "- Each function has a clear, single responsibility\n", "- Easy to test and debug individually"]}, {"cell_type": "code", "execution_count": 3, "id": "a9f6e954", "metadata": {}, "outputs": [], "source": ["# 🔧 FUNCTION 1: Text Processor\n", "# What it does: Takes input text and adds a signature\n", "# Why: This simulates a processing step in our workflow\n", "\n", "def function1(input1):\n", "    \"\"\"\n", "    First processing function in our workflow.\n", "    \n", "    Args:\n", "        input1 (str): The input text to process\n", "    \n", "    Returns:\n", "        str: Processed text with added signature\n", "    \"\"\"\n", "    result = input1 + \" from first function\"\n", "    print(f\"🔄 Function 1 processed: '{input1}' → '{result}'\")\n", "    return result"]}, {"cell_type": "code", "execution_count": 4, "id": "97d1f19e", "metadata": {}, "outputs": [], "source": ["# 🔧 FUNCTION 2: Text Enhancer\n", "# What it does: Takes processed text and adds another signature\n", "# Why: This simulates a second processing step\n", "\n", "def function2(input2):\n", "    \"\"\"\n", "    Second processing function in our workflow.\n", "    \n", "    Args:\n", "        input2 (str): The text from the previous function\n", "    \n", "    Returns:\n", "        str: Further processed text with additional signature\n", "    \"\"\"\n", "    result = input2 + \" savita from second function\"\n", "    print(f\"🔄 Function 2 processed: '{input2}' → '{result}'\")\n", "    return result"]}, {"cell_type": "code", "execution_count": 5, "id": "15cef78c", "metadata": {}, "outputs": [], "source": ["# 🔧 FUNCTION 3: Placeholder for Future Use\n", "# What it does: Currently nothing (placeholder)\n", "# Why: Shows how easy it is to add more functions later\n", "\n", "def function3(input3):\n", "    \"\"\"\n", "    Placeholder function for future workflow expansion.\n", "    \n", "    Args:\n", "        input3: Input for future processing\n", "    \n", "    Returns:\n", "        None: Currently does nothing\n", "    \"\"\"\n", "    # TODO: Add functionality here when needed\n", "    pass"]}, {"cell_type": "markdown", "id": "testing-functions", "metadata": {}, "source": ["## **Step 4: Testing Our Functions Individually**\n", "\n", "Before connecting functions in a workflow, let's test them individually. This is a **best practice** in programming:\n", "\n", "### **🧪 Why Test First:**\n", "- Ensure each function works correctly\n", "- Understand what each function does\n", "- Debug issues before building complex workflows"]}, {"cell_type": "code", "execution_count": 6, "id": "01edcb93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Function 1 processed: 'sunny' → 'sunny from first function'\n"]}, {"data": {"text/plain": ["'sunny from first function'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 🧪 Test Function 1\n", "# Input: \"sunny\"\n", "# Expected Output: \"sunny from first function\"\n", "\n", "test_result_1 = function1(\"sunny\")\n", "test_result_1"]}, {"cell_type": "code", "execution_count": 7, "id": "10399a00", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Function 2 processed: 'savita' → 'savita savita from second function'\n"]}, {"data": {"text/plain": ["'savita savita from second function'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 🧪 Test Function 2\n", "# Input: \"savita\"\n", "# Expected Output: \"savita savita from second function\"\n", "\n", "test_result_2 = function2(\"savita\")\n", "test_result_2"]}, {"cell_type": "markdown", "id": "langgraph-intro", "metadata": {}, "source": ["## **Step 5: Introduction to <PERSON><PERSON><PERSON><PERSON>**\n", "\n", "Now that our functions work individually, let's connect them using LangGraph!\n", "\n", "### **🏗️ What We're Building:**\n", "A workflow that:\n", "1. Takes input text\n", "2. Processes it through Function 1\n", "3. Passes the result to Function 2\n", "4. Returns the final result\n", "\n", "### **📦 Import LangGraph:**\n", "First, we need to import the Graph class from LangGraph:"]}, {"cell_type": "code", "execution_count": 8, "id": "7e01384d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ LangGraph already imported in Step 1!\n"]}], "source": ["# 📦 Import the Graph class from LangGraph (if not already imported)\n", "# What this does: Gives us access to the Graph functionality\n", "# Why we need it: Graph is the main class for building workflows\n", "\n", "# This import is already done in Step 1, but we include it here for clarity\n", "# from langgraph.graph import Graph\n", "print(\"✅ LangGraph already imported in Step 1!\")"]}, {"cell_type": "markdown", "id": "create-graph", "metadata": {}, "source": ["## **Step 6: Creating Our First Graph**\n", "\n", "### **🏭 Creating the Factory (Graph):**\n", "Think of `Graph()` as creating an empty factory building where we'll add our processing stations."]}, {"cell_type": "code", "execution_count": 9, "id": "5c37aea4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Empty workflow created!\n", "📊 Workflow object: <langgraph.graph.graph.Graph object at 0x120e72c50>\n"]}], "source": ["# 🏭 Create an empty workflow (factory)\n", "# What this does: Creates a container for our workflow\n", "# Why: We need a place to add our functions and connections\n", "\n", "workflow1 = Graph()\n", "print(\"✅ Empty workflow created!\")\n", "print(f\"📊 Workflow object: {workflow1}\")"]}, {"cell_type": "markdown", "id": "add-nodes", "metadata": {}, "source": ["## **Step 7: Adding Nodes (Processing Stations)**\n", "\n", "### **🔧 What are Nodes:**\n", "- Nodes are **processing stations** in our factory\n", "- Each node contains a **function** that does specific work\n", "- We give each node a **name** so we can reference it\n", "\n", "### **📝 Syntax:**\n", "```python\n", "workflow.add_node(\"node_name\", function_name)\n", "```"]}, {"cell_type": "code", "execution_count": 10, "id": "e01c1895", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Added 'fun1' node with function1\n"]}], "source": ["# 🔧 Add Node 1: First Processing Station\n", "# What this does: Registers function1 as a processing station named \"fun1\"\n", "# Why: We need to tell the workflow what functions are available\n", "\n", "workflow1.add_node(\"fun1\", function1)\n", "print(\"✅ Added 'fun1' node with function1\")"]}, {"cell_type": "code", "execution_count": 11, "id": "bb380254", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Added 'fun2' node with function2\n"]}], "source": ["# 🔧 Add Node 2: Second Processing Station\n", "# What this does: Registers function2 as a processing station named \"fun2\"\n", "# Why: We need multiple stations for our assembly line\n", "\n", "workflow1.add_node(\"fun2\", function2)\n", "print(\"✅ Added 'fun2' node with function2\")"]}, {"cell_type": "markdown", "id": "add-edges", "metadata": {}, "source": ["## **Step 8: <PERSON><PERSON> (Conveyor Belts)**\n", "\n", "### **🔗 What are Edges:**\n", "- Edges are **conveyor belts** that move data between stations\n", "- They define the **order** of processing\n", "- They tell the workflow: \"After this node, go to that node\"\n", "\n", "### **📝 Syntax:**\n", "```python\n", "workflow.add_edge(\"from_node\", \"to_node\")\n", "```\n", "\n", "### **🔄 Our Flow:**\n", "```\n", "Input → fun1 → fun2 → Output\n", "```"]}, {"cell_type": "code", "execution_count": 12, "id": "e1e86bd4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Connected fun1 → fun2\n", "🔄 Data will flow: Input → fun1 → fun2 → Output\n"]}], "source": ["# 🔗 Add Edge: Connect the Processing Stations\n", "# What this does: Creates a conveyor belt from fun1 to fun2\n", "# Why: Tells the workflow the order of processing\n", "\n", "workflow1.add_edge(\"fun1\", \"fun2\")\n", "print(\"✅ Connected fun1 → fun2\")\n", "print(\"🔄 Data will flow: Input → fun1 → fun2 → Output\")"]}, {"cell_type": "markdown", "id": "entry-exit-points", "metadata": {}, "source": ["## **Step 9: Setting Entry and Exit Points**\n", "\n", "### **🚪 Entry Point:**\n", "- Where the workflow **starts**\n", "- The first node to receive input data\n", "\n", "### **🏁 Exit Point:**\n", "- Where the workflow **ends**\n", "- The last node that produces final output"]}, {"cell_type": "code", "execution_count": 13, "id": "9498cc80", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Set entry point: fun1\n", "🚪 Workflow will start at fun1\n"]}], "source": ["# 🚪 Set Entry Point: Where to Start\n", "# What this does: Tells the workflow to start at fun1\n", "# Why: The workflow needs to know where to begin processing\n", "\n", "workflow1.set_entry_point(\"fun1\")\n", "print(\"✅ Set entry point: fun1\")\n", "print(\"🚪 Workflow will start at fun1\")"]}, {"cell_type": "code", "execution_count": 14, "id": "13c17aa0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Set exit point: fun2\n", "🏁 Workflow will end at fun2\n"]}], "source": ["# 🏁 Set Exit Point: Where to End\n", "# What this does: Tells the workflow to end at fun2\n", "# Why: The workflow needs to know where to stop and return results\n", "\n", "workflow1.set_finish_point(\"fun2\")\n", "print(\"✅ Set exit point: fun2\")\n", "print(\"🏁 Workflow will end at fun2\")"]}, {"cell_type": "markdown", "id": "compile-workflow", "metadata": {}, "source": ["## **Step 10: Compiling the Workflow**\n", "\n", "### **⚙️ What is Compilation:**\n", "- **Compilation** turns our workflow blueprint into an executable application\n", "- It validates all connections and prepares the workflow for execution\n", "- Think of it as \"turning on the factory\" - making it ready to process data\n", "\n", "### **📝 Syntax:**\n", "```python\n", "app = workflow.compile()\n", "```"]}, {"cell_type": "code", "execution_count": 15, "id": "d154dc22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Workflow compiled successfully!\n", "🏭 Factory is now ready to process data!\n"]}], "source": ["# ⚙️ Compile the Workflow: Turn Blueprint into Executable App\n", "# What this does: Validates connections and prepares workflow for execution\n", "# Why: We need to \"turn on the factory\" before we can process data\n", "\n", "app = workflow1.compile()\n", "print(\"✅ Workflow compiled successfully!\")\n", "print(\"🏭 Factory is now ready to process data!\")"]}, {"cell_type": "markdown", "id": "inspect-graph", "metadata": {}, "source": ["## **Step 11: Inspecting the Workflow Structure**\n", "\n", "Let's examine what our compiled workflow looks like internally:"]}, {"cell_type": "code", "execution_count": 16, "id": "e3d2676b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Workflow Structure:\n", "✅ Nodes: fun1, fun2, __start__, __end__\n", "✅ Edges: __start__ → fun1 → fun2 → __end__\n"]}, {"data": {"text/plain": ["Graph(nodes={'fun1': Node(id='fun1', name='fun1', data=fun1(tags=None, recurse=True, explode_args=False, func_accepts_config=False, func_accepts={}), metadata=None), 'fun2': Node(id='fun2', name='fun2', data=fun2(tags=None, recurse=True, explode_args=False, func_accepts_config=False, func_accepts={}), metadata=None), '__start__': Node(id='__start__', name='__start__', data=None, metadata=None), '__end__': Node(id='__end__', name='__end__', data=None, metadata=None)}, edges=[Edge(source='__start__', target='fun1', data=None, conditional=False), Edge(source='fun1', target='fun2', data=None, conditional=False), Edge(source='fun2', target='__end__', data=None, conditional=False)])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# 📊 Inspect Workflow Structure\n", "# What this shows: Internal structure of our compiled workflow\n", "# Notice: LangGraph automatically added __start__ and __end__ nodes\n", "\n", "print(\"📊 Workflow Structure:\")\n", "print(\"✅ Nodes: fun1, fun2, __start__, __end__\")\n", "print(\"✅ Edges: __start__ → fun1 → fun2 → __end__\")\n", "\n", "app.get_graph()"]}, {"cell_type": "code", "execution_count": 17, "id": "fc22b9a1", "metadata": {}, "outputs": [], "source": ["from IPython.display import Image, display"]}, {"cell_type": "code", "execution_count": 18, "id": "15d0ec3a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Image(app.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 19, "id": "363f212f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Function 1 processed: 'hi this is sunny' → 'hi this is sunny from first function'\n", "🔄 Function 2 processed: 'hi this is sunny from first function' → 'hi this is sunny from first function savita from second function'\n"]}, {"data": {"text/plain": ["'hi this is sunny from first function savita from second function'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["app.invoke(\"hi this is sunny\")"]}, {"cell_type": "code", "execution_count": 20, "id": "e84c550d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Function 1 processed: 'hi this is rohit' → 'hi this is rohit from first function'\n", "here is output from fun1\n", "_______\n", "hi this is rohit from first function\n", "\n", "\n", "🔄 Function 2 processed: 'hi this is rohit from first function' → 'hi this is rohit from first function savita from second function'\n", "here is output from fun2\n", "_______\n", "hi this is rohit from first function savita from second function\n", "\n", "\n"]}], "source": ["for output in app.stream(\"hi this is rohit\"):\n", "    for key,value in output.items():\n", "        print(f\"here is output from {key}\")\n", "        print(\"_______\")\n", "        print(value)\n", "        print(\"\\n\")\n", "    "]}, {"cell_type": "code", "execution_count": 21, "id": "a2705963", "metadata": {}, "outputs": [{"ename": "DefaultCredentialsError", "evalue": "Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mDefaultCredentialsError\u001b[0m                   <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[21], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mlangchain_google_genai\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ChatGoogleGenerativeAI\n\u001b[0;32m----> 2\u001b[0m model\u001b[38;5;241m=\u001b[39m\u001b[43mChatGoogleGenerativeAI\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mgemini-1.5-flash\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages/langchain_google_genai/chat_models.py:1108\u001b[0m, in \u001b[0;36mChatGoogleGenerativeAI.__init__\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m   1101\u001b[0m         suggestion \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m   1102\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m Did you mean: \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msuggestions[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m?\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m suggestions \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1103\u001b[0m         )\n\u001b[1;32m   1104\u001b[0m         logger\u001b[38;5;241m.\u001b[39mwarning(\n\u001b[1;32m   1105\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnexpected argument \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00marg\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1106\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprovided to ChatGoogleGenerativeAI.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msuggestion\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1107\u001b[0m         )\n\u001b[0;32m-> 1108\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages/langchain_core/load/serializable.py:130\u001b[0m, in \u001b[0;36mSerializable.__init__\u001b[0;34m(self, *args, **kwargs)\u001b[0m\n\u001b[1;32m    128\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39margs: Any, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    129\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\"\"\"\u001b[39;00m  \u001b[38;5;66;03m# noqa: D419\u001b[39;00m\n\u001b[0;32m--> 130\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "    \u001b[0;31m[... skipping hidden 1 frame]\u001b[0m\n", "File \u001b[0;32m/opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages/langchain_google_genai/chat_models.py:1169\u001b[0m, in \u001b[0;36mChatGoogleGenerativeAI.validate_environment\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1167\u001b[0m         google_api_key \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgoogle_api_key\n\u001b[1;32m   1168\u001b[0m transport: Optional[\u001b[38;5;28mstr\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtransport\n\u001b[0;32m-> 1169\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclient \u001b[38;5;241m=\u001b[39m \u001b[43mgenaix\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbuild_generative_service\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1170\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcredentials\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcredentials\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1171\u001b[0m \u001b[43m    \u001b[49m\u001b[43mapi_key\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mgoogle_api_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1172\u001b[0m \u001b[43m    \u001b[49m\u001b[43mclient_info\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mclient_info\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1173\u001b[0m \u001b[43m    \u001b[49m\u001b[43mclient_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclient_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1174\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtransport\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtransport\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1175\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1176\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39masync_client_running \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1177\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n", "File \u001b[0;32m/opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages/langchain_google_genai/_genai_extension.py:276\u001b[0m, in \u001b[0;36mbuild_generative_service\u001b[0;34m(credentials, api_key, client_options, client_info, transport)\u001b[0m\n\u001b[1;32m    262\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mbuild_generative_service\u001b[39m(\n\u001b[1;32m    263\u001b[0m     credentials: Optional[credentials\u001b[38;5;241m.\u001b[39mCredentials] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    264\u001b[0m     api_key: Optional[\u001b[38;5;28mstr\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    267\u001b[0m     transport: Optional[\u001b[38;5;28mstr\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m,\n\u001b[1;32m    268\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m v1betaGenerativeServiceClient:\n\u001b[1;32m    269\u001b[0m     config \u001b[38;5;241m=\u001b[39m _prepare_config(\n\u001b[1;32m    270\u001b[0m         credentials\u001b[38;5;241m=\u001b[39mcredentials,\n\u001b[1;32m    271\u001b[0m         api_key\u001b[38;5;241m=\u001b[39mapi_key,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    274\u001b[0m         client_info\u001b[38;5;241m=\u001b[39mclient_info,\n\u001b[1;32m    275\u001b[0m     )\n\u001b[0;32m--> 276\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mv1betaGenerativeServiceClient\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mconfig\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/client.py:697\u001b[0m, in \u001b[0;36mGenerativeServiceClient.__init__\u001b[0;34m(self, credentials, transport, client_options, client_info)\u001b[0m\n\u001b[1;32m    688\u001b[0m     transport_init: Union[\n\u001b[1;32m    689\u001b[0m         Type[GenerativeServiceTransport],\n\u001b[1;32m    690\u001b[0m         Callable[\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m, GenerativeServiceTransport],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    694\u001b[0m         \u001b[38;5;28;01melse\u001b[39;00m cast(Callable[\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;241m.\u001b[39m, GenerativeServiceTransport], transport)\n\u001b[1;32m    695\u001b[0m     )\n\u001b[1;32m    696\u001b[0m     \u001b[38;5;66;03m# initialize with the provided callable or the passed in class\u001b[39;00m\n\u001b[0;32m--> 697\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_transport \u001b[38;5;241m=\u001b[39m \u001b[43mtransport_init\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    698\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcredentials\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcredentials\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    699\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcredentials_file\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client_options\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcredentials_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    700\u001b[0m \u001b[43m        \u001b[49m\u001b[43mhost\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_api_endpoint\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    701\u001b[0m \u001b[43m        \u001b[49m\u001b[43mscopes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client_options\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mscopes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    702\u001b[0m \u001b[43m        \u001b[49m\u001b[43mclient_cert_source_for_mtls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client_cert_source\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    703\u001b[0m \u001b[43m        \u001b[49m\u001b[43mquota_project_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client_options\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mquota_project_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    704\u001b[0m \u001b[43m        \u001b[49m\u001b[43mclient_info\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mclient_info\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    705\u001b[0m \u001b[43m        \u001b[49m\u001b[43malways_use_jwt_access\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    706\u001b[0m \u001b[43m        \u001b[49m\u001b[43mapi_audience\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_client_options\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapi_audience\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    707\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    709\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124masync\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_transport):\n\u001b[1;32m    710\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m CLIENT_LOGGING_SUPPORTED \u001b[38;5;129;01mand\u001b[39;00m _LOGGER\u001b[38;5;241m.\u001b[39misEnabledFor(\n\u001b[1;32m    711\u001b[0m         std_logging\u001b[38;5;241m.\u001b[39mDEBUG\n\u001b[1;32m    712\u001b[0m     ):  \u001b[38;5;66;03m# pragma: NO COVER\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/transports/grpc.py:234\u001b[0m, in \u001b[0;36mGenerativeServiceGrpcTransport.__init__\u001b[0;34m(self, host, credentials, credentials_file, scopes, channel, api_mtls_endpoint, client_cert_source, ssl_channel_credentials, client_cert_source_for_mtls, quota_project_id, client_info, always_use_jwt_access, api_audience)\u001b[0m\n\u001b[1;32m    229\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_ssl_channel_credentials \u001b[38;5;241m=\u001b[39m grpc\u001b[38;5;241m.\u001b[39mssl_channel_credentials(\n\u001b[1;32m    230\u001b[0m                 certificate_chain\u001b[38;5;241m=\u001b[39mcert, private_key\u001b[38;5;241m=\u001b[39mkey\n\u001b[1;32m    231\u001b[0m             )\n\u001b[1;32m    233\u001b[0m \u001b[38;5;66;03m# The base transport sets the host, credentials and scopes\u001b[39;00m\n\u001b[0;32m--> 234\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m    235\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhost\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhost\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    236\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcredentials\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcredentials\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    237\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcredentials_file\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcredentials_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    238\u001b[0m \u001b[43m    \u001b[49m\u001b[43mscopes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mscopes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    239\u001b[0m \u001b[43m    \u001b[49m\u001b[43mquota_project_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquota_project_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    240\u001b[0m \u001b[43m    \u001b[49m\u001b[43mclient_info\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mclient_info\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    241\u001b[0m \u001b[43m    \u001b[49m\u001b[43malways_use_jwt_access\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43malways_use_jwt_access\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    242\u001b[0m \u001b[43m    \u001b[49m\u001b[43mapi_audience\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mapi_audience\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    243\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    245\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_grpc_channel:\n\u001b[1;32m    246\u001b[0m     \u001b[38;5;66;03m# initialize with the provided callable or the default channel\u001b[39;00m\n\u001b[1;32m    247\u001b[0m     channel_init \u001b[38;5;241m=\u001b[39m channel \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m)\u001b[38;5;241m.\u001b[39mcreate_channel\n", "File \u001b[0;32m/opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/transports/base.py:100\u001b[0m, in \u001b[0;36mGenerativeServiceTransport.__init__\u001b[0;34m(self, host, credentials, credentials_file, scopes, quota_project_id, client_info, always_use_jwt_access, api_audience, **kwargs)\u001b[0m\n\u001b[1;32m     96\u001b[0m     credentials, _ \u001b[38;5;241m=\u001b[39m google\u001b[38;5;241m.\u001b[39mauth\u001b[38;5;241m.\u001b[39mload_credentials_from_file(\n\u001b[1;32m     97\u001b[0m         credentials_file, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mscopes_kwargs, quota_project_id\u001b[38;5;241m=\u001b[39mquota_project_id\n\u001b[1;32m     98\u001b[0m     )\n\u001b[1;32m     99\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m credentials \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_ignore_credentials:\n\u001b[0;32m--> 100\u001b[0m     credentials, _ \u001b[38;5;241m=\u001b[39m \u001b[43mgoogle\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mauth\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdefault\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    101\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mscopes_kwargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mquota_project_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mquota_project_id\u001b[49m\n\u001b[1;32m    102\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    103\u001b[0m     \u001b[38;5;66;03m# Don't apply audience if the credentials file passed from user.\u001b[39;00m\n\u001b[1;32m    104\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(credentials, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwith_gdch_audience\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n", "File \u001b[0;32m/opt/anaconda3/envs/agentic-2/lib/python3.10/site-packages/google/auth/_default.py:685\u001b[0m, in \u001b[0;36mdefault\u001b[0;34m(scopes, request, quota_project_id, default_scopes)\u001b[0m\n\u001b[1;32m    677\u001b[0m             _LOGGER\u001b[38;5;241m.\u001b[39mwarning(\n\u001b[1;32m    678\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo project ID could be determined. Consider running \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    679\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`gcloud config set project` or setting the \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    680\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124menvironment variable\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    681\u001b[0m                 environment_vars\u001b[38;5;241m.\u001b[39mPROJECT,\n\u001b[1;32m    682\u001b[0m             )\n\u001b[1;32m    683\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m credentials, effective_project_id\n\u001b[0;32m--> 685\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exceptions\u001b[38;5;241m.\u001b[39mDefaultCredentialsError(_CLOUD_SDK_MISSING_CREDENTIALS)\n", "\u001b[0;31mDefaultCredentialsError\u001b[0m: Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc for more information."]}], "source": [" from langchain_google_genai import ChatGoogleGenerativeAI\n", "model=ChatGoogleGenerativeAI(model='gemini-1.5-flash')"]}, {"cell_type": "code", "execution_count": null, "id": "e71fecfc", "metadata": {}, "outputs": [], "source": ["model.invoke(\"hi\").content"]}, {"cell_type": "code", "execution_count": null, "id": "1c11b65a", "metadata": {}, "outputs": [], "source": ["def llm(input):\n", "    from langchain_google_genai import ChatGoogleGenerativeAI\n", "    model=ChatGoogleGenerativeAI(model='gemini-1.5-flash')\n", "    output=model.invoke(input)\n", "    return output.content"]}, {"cell_type": "code", "execution_count": null, "id": "07be4106", "metadata": {}, "outputs": [], "source": ["def token_counter(input):\n", "    token=input.split()\n", "    token_number=len(token)\n", "    return f\"total token number in the generated answer is {token_number}\""]}, {"cell_type": "code", "execution_count": null, "id": "54872a36", "metadata": {}, "outputs": [], "source": ["workflow2=Graph()"]}, {"cell_type": "code", "execution_count": null, "id": "4cb4d7cf", "metadata": {}, "outputs": [], "source": ["workflow2.add_node(\"My_LLM\",llm)"]}, {"cell_type": "code", "execution_count": null, "id": "62df1dd3", "metadata": {}, "outputs": [], "source": ["workflow2.add_node(\"LLM_Output_Token_Counter\",token_counter)"]}, {"cell_type": "code", "execution_count": null, "id": "e8a4cccc", "metadata": {}, "outputs": [], "source": ["workflow2.add_edge(\"My_LLM\",\"LLM_Output_Token_Counter\")"]}, {"cell_type": "code", "execution_count": null, "id": "993ae266", "metadata": {}, "outputs": [], "source": ["workflow2.set_entry_point(\"My_LLM\")"]}, {"cell_type": "code", "execution_count": null, "id": "5e7d269e", "metadata": {}, "outputs": [], "source": ["workflow2.set_finish_point(\"LLM_Output_Token_Counter\")"]}, {"cell_type": "code", "execution_count": null, "id": "59f30084", "metadata": {}, "outputs": [], "source": ["app=workflow2.compile()"]}, {"cell_type": "code", "execution_count": null, "id": "7c5715fe", "metadata": {}, "outputs": [], "source": ["display(Image(app.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": null, "id": "b509b51e", "metadata": {}, "outputs": [], "source": ["app.invoke(\"can you tell me about the india's capital?\")"]}, {"cell_type": "code", "execution_count": null, "id": "e3320744", "metadata": {}, "outputs": [], "source": ["app.invoke(\"tell me about the tata enterpirse in very detail.\")"]}, {"cell_type": "code", "execution_count": null, "id": "98f8aff1", "metadata": {}, "outputs": [], "source": ["for output in app.stream(\"tell me about the tata enterpirse in very detail.\"):\n", "    for key,value in output.items():\n", "        print(f\"here is output from {key}\")\n", "        print(\"_______\")\n", "        print(value)\n", "        print(\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "cde3746a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (agentic-2)", "language": "python", "name": "agentic-2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}